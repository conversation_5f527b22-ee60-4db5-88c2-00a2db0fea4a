<?php
/**
 * Application Configuration
 * Redolence Salon & Spa
 */

// Environment settings
define('APP_ENV', 'development'); // development, production
define('APP_DEBUG', true);
define('APP_NAME', 'Redolence Salon & Spa');
define('APP_TAGLINE', 'Your Beauty, Our Passion');
define('APP_URL', 'http://localhost');
define('APP_VERSION', '1.0.0');

// Paths
define('ROOT_PATH', dirname(__DIR__));
define('INCLUDES_PATH', ROOT_PATH . '/includes');
define('UPLOADS_PATH', ROOT_PATH . '/uploads');
define('ASSETS_PATH', ROOT_PATH . '/assets');

// URLs
define('BASE_URL', APP_URL);
define('ASSETS_URL', BASE_URL . '/assets');
define('UPLOADS_URL', BASE_URL . '/uploads');

// Security
define('SESSION_LIFETIME', 3600); // 1 hour
define('CSRF_TOKEN_NAME', '_token');
define('PASSWORD_MIN_LENGTH', 8);
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 minutes

// File upload settings
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);
define('ALLOWED_DOCUMENT_TYPES', ['pdf', 'doc', 'docx']);

// Pagination
define('ITEMS_PER_PAGE', 20);
define('ADMIN_ITEMS_PER_PAGE', 25);

// Business settings
define('BUSINESS_PHONE', '+255 123 456 789');
define('BUSINESS_EMAIL', '<EMAIL>');
define('BUSINESS_ADDRESS', '123 Beauty Street, Dar es Salaam, Tanzania');
define('BUSINESS_HOURS', 'Mon-Sat: 9:00 AM - 8:00 PM, Sun: 10:00 AM - 6:00 PM');

// Social media
define('SOCIAL_FACEBOOK', 'https://facebook.com/redolence');
define('SOCIAL_INSTAGRAM', 'https://instagram.com/redolence');
define('SOCIAL_TWITTER', 'https://twitter.com/redolence');
define('SOCIAL_YOUTUBE', 'https://youtube.com/redolence');

// Color palette
define('PRIMARY_GREEN', '#49a75c');
define('PRIMARY_BLUE', '#5894d2');
define('BACKGROUND_COLOR', '#ffffff');
define('TEXT_DARK', '#333333');
define('TEXT_LIGHT', '#666666');
define('ACCENT_COLOR', '#f8f9fa');

// Booking settings
define('BOOKING_ADVANCE_DAYS', 30);
define('BOOKING_CANCEL_HOURS', 24);
define('DEFAULT_SERVICE_DURATION', 60); // minutes

// Email settings
define('SMTP_FROM_EMAIL', '<EMAIL>');
define('SMTP_FROM_NAME', APP_NAME);

// Timezone
date_default_timezone_set('Africa/Dar_es_Salaam');

// Error reporting
if (APP_DEBUG) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// Session configuration
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', 0); // Set to 1 for HTTPS
ini_set('session.use_strict_mode', 1);
ini_set('session.cookie_samesite', 'Strict');
