/**
 * Main Stylesheet with Tailwind CSS
 * Redolence Salon & Spa
 */

@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Custom Base Styles */
@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    @apply font-primary text-base leading-relaxed text-text-dark bg-background;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-secondary font-semibold leading-tight mb-4;
  }

  h1 { @apply text-5xl; }
  h2 { @apply text-4xl; }
  h3 { @apply text-3xl; }
  h4 { @apply text-2xl; }
  h5 { @apply text-xl; }
  h6 { @apply text-lg; }

  p {
    @apply mb-4 text-text-light;
  }

  a {
    @apply text-primary-green no-underline transition-all duration-300;
  }

  a:hover {
    @apply text-primary-blue;
  }
}

/* Custom Components */
@layer components {
  /* Container */
  .container {
    @apply max-w-7xl mx-auto px-5;
  }

  /* Buttons */
  .btn {
    @apply inline-block px-6 py-3 text-base font-medium text-center no-underline border-2 border-transparent rounded cursor-pointer transition-all duration-300 leading-none;
  }

  .btn-primary {
    @apply bg-primary-green text-white border-primary-green;
  }

  .btn-primary:hover {
    @apply bg-primary-blue border-primary-blue text-white;
  }

  .btn-outline {
    @apply bg-transparent text-primary-green border-primary-green;
  }

  .btn-outline:hover {
    @apply bg-primary-green text-white;
  }

  .btn-secondary {
    @apply bg-primary-blue text-white border-primary-blue;
  }

  .btn-secondary:hover {
    @apply bg-primary-green border-primary-green text-white;
  }

  .btn-lg {
    @apply px-8 py-4 text-lg;
  }

  .btn-sm {
    @apply px-4 py-2 text-sm;
  }

  /* Top Bar */
  .top-bar {
    @apply bg-text-dark text-white py-2 text-sm;
  }

  .top-bar-content {
    @apply flex justify-between items-center;
  }

  .contact-info span {
    @apply mr-5;
  }

  .contact-info i {
    @apply mr-1 text-primary-green;
  }

  .social-links a {
    @apply text-white ml-2 text-sm transition-all duration-300;
  }

  .social-links a:hover {
    @apply text-primary-green;
  }

  /* Main Header */
  .main-header {
    @apply bg-white shadow-light sticky top-0 z-50;
  }

  .header-content {
    @apply flex items-center justify-between py-4;
  }

  /* Logo */
  .logo a {
    @apply flex items-center no-underline;
  }

  .logo-img {
    @apply h-12 mr-4;
  }

  .logo-text h1 {
    @apply text-2xl text-primary-green m-0;
  }

  .logo-text span {
    @apply text-sm text-text-light italic;
  }

  /* Navigation */
  .main-nav {
    @apply flex-1 mx-10;
  }

  .nav-menu {
    @apply flex list-none justify-center;
  }

  .nav-menu li {
    @apply relative mx-4;
  }

  .nav-menu a {
    @apply text-text-dark font-medium py-2 block transition-all duration-300;
  }

  .nav-menu a:hover,
  .nav-menu a.active {
    @apply text-primary-green;
  }

  /* Dropdown */
  .dropdown:hover .dropdown-menu {
    @apply opacity-100 visible translate-y-0;
  }

  .dropdown-menu {
    @apply absolute top-full left-0 bg-white shadow-medium rounded min-w-[200px] opacity-0 invisible -translate-y-2 transition-all duration-300 z-50;
  }

  .dropdown-menu li {
    @apply m-0;
  }

  .dropdown-menu a {
    @apply px-5 py-3 border-b border-border;
  }

  .dropdown-menu a:hover {
    @apply bg-accent;
  }

  /* Header Actions */
  .header-actions {
    @apply flex items-center gap-4;
  }

  .user-menu {
    @apply relative;
  }

  .user-toggle {
    @apply flex items-center gap-2 text-text-dark font-medium;
  }

  /* Mobile Menu */
  .mobile-menu-toggle {
    @apply hidden flex-col bg-transparent border-none cursor-pointer p-1;
  }

  .mobile-menu-toggle span {
    @apply w-6 h-0.5 bg-text-dark my-0.5 transition-all duration-300;
  }

  .mobile-nav {
    @apply hidden bg-white shadow-light;
  }

  .mobile-nav-menu {
    @apply list-none p-5;
  }

  .mobile-nav-menu li {
    @apply my-2;
  }

  .mobile-nav-menu a {
    @apply text-text-dark font-medium py-2 block border-b border-border;
  }

  /* Flash Messages */
  .flash-message {
    @apply py-4 mb-0 relative;
  }

  .flash-message .container {
    @apply flex justify-between items-center;
  }

  .flash-info { @apply bg-blue-100 text-blue-800; }
  .flash-success { @apply bg-green-100 text-green-800; }
  .flash-warning { @apply bg-yellow-100 text-yellow-800; }
  .flash-error { @apply bg-red-100 text-red-800; }

  .flash-close {
    @apply bg-transparent border-none text-xl cursor-pointer;
  }

  /* Back to Top Button */
  .back-to-top {
    @apply fixed bottom-8 right-8 w-12 h-12 bg-primary-green text-white border-none rounded-full cursor-pointer opacity-0 invisible transition-all duration-300 z-50 flex items-center justify-center;
  }

  .back-to-top.show {
    @apply opacity-100 visible;
  }

  .back-to-top:hover {
    @apply bg-primary-blue -translate-y-0.5;
  }

  /* Notifications */
  .notification {
    @apply fixed top-5 right-5 px-5 py-4 rounded text-white z-[1001] translate-x-full transition-all duration-300 max-w-xs;
  }

  .notification.show {
    @apply translate-x-0;
  }

  .notification-info { @apply bg-primary-blue; }
  .notification-success { @apply bg-primary-green; }
  .notification-warning { @apply bg-yellow-500; }
  .notification-error { @apply bg-red-500; }

  .notification-close {
    @apply bg-transparent border-none text-white text-lg cursor-pointer float-right ml-2;
  }

  /* Form Styles */
  .form-group {
    @apply mb-5;
  }

  .form-label {
    @apply block mb-1 font-medium text-text-dark;
  }

  .form-control {
    @apply w-full px-4 py-3 border-2 border-border rounded text-base transition-all duration-300;
  }

  .form-control:focus {
    @apply outline-none border-primary-green shadow-[0_0_0_3px_rgba(73,167,92,0.1)];
  }

  .form-control.error {
    @apply border-red-500;
  }

  .field-error {
    @apply text-red-500 text-sm mt-1;
  }

  .input-group {
    @apply flex;
  }

  .input-group .form-control {
    @apply rounded-r-none;
  }

  .input-group .btn {
    @apply rounded-l-none;
  }

  /* Utility Classes */
  .section-header {
    @apply mb-12;
  }

  .section-subtitle {
    @apply text-lg text-text-light -mt-2;
  }
}

/* Custom Utilities */
@layer utilities {
  .img-fluid {
    @apply max-w-full h-auto;
  }

  .text-shadow {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  }

  .gradient-overlay {
    background: linear-gradient(135deg, rgba(73, 167, 92, 0.8), rgba(88, 148, 210, 0.6));
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .top-bar-content {
    @apply flex-col gap-2 text-center;
  }

  .contact-info span {
    @apply mx-2 block mb-1;
  }

  .main-nav {
    @apply hidden;
  }

  .mobile-menu-toggle {
    @apply flex;
  }

  .header-actions .btn {
    @apply px-4 py-2 text-sm;
  }

  .logo-text h1 {
    @apply text-xl;
  }

  .back-to-top {
    @apply bottom-5 right-5 w-11 h-11;
  }

  .notification {
    @apply top-2 right-2 left-2 max-w-none;
  }
}
