/**
 * Redolence Salon & Spa - Luxury Design System
 * Custom CSS that overrides Tailwind defaults
 */

/* Reset and Base Styles */
* {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: 'Poppins', sans-serif !important;
    font-size: 1rem !important;
    line-height: 1.6 !important;
    color: #1f2937 !important;
    background-color: #ffffff !important;
    margin: 0 !important;
    padding: 0 !important;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Playfair Display', serif !important;
    font-weight: 600 !important;
    line-height: 1.2 !important;
    margin-bottom: 1.5rem !important;
    color: #111827 !important;
    letter-spacing: -0.025em !important;
}

h1 { font-size: 3rem !important; }
h2 { font-size: 2.5rem !important; }
h3 { font-size: 2rem !important; }
h4 { font-size: 1.75rem !important; }
h5 { font-size: 1.5rem !important; }
h6 { font-size: 1.25rem !important; }

p {
    margin-bottom: 1.5rem !important;
    color: #6b7280 !important;
    line-height: 1.7 !important;
    font-size: 1.125rem !important;
}

a {
    color: #49a75c !important;
    text-decoration: none !important;
    transition: all 0.3s ease !important;
}

a:hover {
    color: #5894d2 !important;
}

/* Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f3f4f6;
}

::-webkit-scrollbar-thumb {
    background: #49a75c;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #5894d2;
}

/* Button Styles */
.btn {
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 1rem 2rem !important;
    font-weight: 600 !important;
    text-align: center !important;
    text-decoration: none !important;
    border: 2px solid !important;
    border-radius: 0.75rem !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    font-size: 1rem !important;
    letter-spacing: 0.025em !important;
    min-height: 3.5rem !important;
    position: relative !important;
    overflow: hidden !important;
}

.btn-primary {
    background: linear-gradient(135deg, #49a75c, #3d8b4e) !important;
    color: white !important;
    border-color: #49a75c !important;
    box-shadow: 0 10px 15px -3px rgba(73, 167, 92, 0.3) !important;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5894d2, #4a7bc8) !important;
    border-color: #5894d2 !important;
    box-shadow: 0 20px 25px -5px rgba(88, 148, 210, 0.4) !important;
    transform: translateY(-2px) !important;
    color: white !important;
}

.btn-secondary {
    background: transparent !important;
    color: #49a75c !important;
    border-color: #49a75c !important;
}

.btn-secondary:hover {
    background: #49a75c !important;
    color: white !important;
    box-shadow: 0 10px 15px -3px rgba(73, 167, 92, 0.3) !important;
    transform: translateY(-2px) !important;
}

.btn-outline {
    background: transparent !important;
    color: #374151 !important;
    border-color: #d1d5db !important;
}

.btn-outline:hover {
    background: #374151 !important;
    color: white !important;
    border-color: #374151 !important;
    box-shadow: 0 10px 15px -3px rgba(55, 65, 81, 0.3) !important;
    transform: translateY(-2px) !important;
}

.btn-lg {
    padding: 1.25rem 3rem !important;
    font-size: 1.125rem !important;
    min-height: 4rem !important;
}

.btn-sm {
    padding: 0.75rem 1.5rem !important;
    font-size: 0.875rem !important;
    min-height: 2.75rem !important;
}

/* Top Bar */
.top-bar {
    background: linear-gradient(135deg, #1f2937, #111827) !important;
    color: white !important;
    padding: 0.75rem 0 !important;
    font-size: 0.875rem !important;
    border-bottom: 1px solid rgba(107, 114, 128, 0.3) !important;
}

.top-bar-content {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    max-width: 1280px !important;
    margin: 0 auto !important;
    padding: 0 1.5rem !important;
}

.contact-info {
    display: flex !important;
    align-items: center !important;
    gap: 1.5rem !important;
}

.contact-info span {
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
    color: #d1d5db !important;
}

.contact-info i {
    color: #49a75c !important;
    font-size: 0.875rem !important;
}

.social-links {
    display: flex !important;
    align-items: center !important;
    gap: 0.75rem !important;
}

.social-links a {
    width: 2rem !important;
    height: 2rem !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-radius: 50% !important;
    background: rgba(255, 255, 255, 0.1) !important;
    color: white !important;
    font-size: 0.875rem !important;
    transition: all 0.3s ease !important;
    text-decoration: none !important;
}

.social-links a:hover {
    background: #49a75c !important;
    color: white !important;
    transform: scale(1.1) !important;
}

/* Main Header */
.main-header {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px) !important;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1) !important;
    position: sticky !important;
    top: 0 !important;
    z-index: 50 !important;
    border-bottom: 1px solid #f3f4f6 !important;
}

.header-content {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    padding: 1.5rem 0 !important;
    max-width: 1280px !important;
    margin: 0 auto !important;
    padding-left: 1.5rem !important;
    padding-right: 1.5rem !important;
}

/* Logo */
.logo a {
    display: flex !important;
    align-items: center !important;
    text-decoration: none !important;
    transition: transform 0.3s ease !important;
}

.logo a:hover {
    transform: scale(1.05) !important;
}

.logo-img {
    height: 3.5rem !important;
    margin-right: 1rem !important;
}

.logo-text h1 {
    font-size: 1.875rem !important;
    color: #49a75c !important;
    margin: 0 !important;
    font-weight: 700 !important;
    letter-spacing: -0.025em !important;
    background: linear-gradient(135deg, #49a75c, #5894d2) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
}

.logo-text span {
    font-size: 0.875rem !important;
    color: #6b7280 !important;
    font-style: italic !important;
    font-weight: 300 !important;
    letter-spacing: 0.05em !important;
}

/* Navigation */
.main-nav {
    flex: 1 !important;
    margin: 0 3rem !important;
}

.nav-menu {
    display: flex !important;
    list-style: none !important;
    justify-content: center !important;
    gap: 2rem !important;
    margin: 0 !important;
    padding: 0 !important;
}

.nav-menu li {
    position: relative !important;
}

.nav-menu a {
    color: #374151 !important;
    font-weight: 500 !important;
    padding: 0.75rem 1rem !important;
    display: block !important;
    transition: all 0.3s ease !important;
    position: relative !important;
    border-radius: 0.5rem !important;
    letter-spacing: 0.025em !important;
    text-decoration: none !important;
}

.nav-menu a::before {
    content: '';
    position: absolute !important;
    bottom: 0 !important;
    left: 0 !important;
    width: 0 !important;
    height: 2px !important;
    background: linear-gradient(135deg, #49a75c, #5894d2) !important;
    transition: all 0.3s ease !important;
}

.nav-menu a:hover::before,
.nav-menu a.active::before {
    width: 100% !important;
}

.nav-menu a:hover,
.nav-menu a.active {
    color: #49a75c !important;
    background-color: rgba(73, 167, 92, 0.05) !important;
}

/* Header Actions */
.header-actions {
    display: flex !important;
    align-items: center !important;
    gap: 1rem !important;
}

.header-phone {
    display: flex !important;
    align-items: center !important;
    gap: 0.75rem !important;
    color: #374151 !important;
    font-weight: 500 !important;
    text-decoration: none !important;
}

.header-phone i {
    color: #49a75c !important;
    font-size: 1.125rem !important;
}

.header-phone span {
    font-size: 1.125rem !important;
    font-weight: 600 !important;
}

/* Hero Section */
.hero-overlay {
    position: absolute !important;
    top: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    left: 0 !important;
    background: linear-gradient(to right, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.2)) !important;
}

.hero-content {
    position: relative !important;
    z-index: 10 !important;
    color: white !important;
}

/* Section Styling */
.section-padding {
    padding-top: 5rem !important;
    padding-bottom: 5rem !important;
}

.section-title {
    text-align: center !important;
    margin-bottom: 4rem !important;
}

.section-subtitle {
    color: #49a75c !important;
    font-weight: 500 !important;
    font-size: 1.125rem !important;
    margin-bottom: 1rem !important;
    letter-spacing: 0.1em !important;
    text-transform: uppercase !important;
}

/* Cards */
.card {
    background-color: white !important;
    border-radius: 1rem !important;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1) !important;
    border: 1px solid #f3f4f6 !important;
    overflow: hidden !important;
    transition: all 0.3s ease !important;
}

.card:hover {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
    transform: translateY(-8px) !important;
}

/* Utility Classes */
.luxury-gradient {
    background: linear-gradient(135deg, #49a75c 0%, #5894d2 100%) !important;
}

.luxury-text-gradient {
    background: linear-gradient(135deg, #49a75c 0%, #5894d2 100%) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
}

/* Container */
.container {
    max-width: 1280px !important;
    margin: 0 auto !important;
    padding-left: 1.5rem !important;
    padding-right: 1.5rem !important;
}

/* Flash Messages */
.flash-message {
    padding: 1rem 0 !important;
    margin-bottom: 0 !important;
    position: relative !important;
}

.flash-message .container {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
}

.flash-info {
    background-color: #dbeafe !important;
    color: #1e40af !important;
}

.flash-success {
    background-color: #dcfce7 !important;
    color: #166534 !important;
}

.flash-warning {
    background-color: #fef3c7 !important;
    color: #92400e !important;
}

.flash-error {
    background-color: #fee2e2 !important;
    color: #dc2626 !important;
}

.flash-close {
    background: transparent !important;
    border: none !important;
    font-size: 1.25rem !important;
    cursor: pointer !important;
}

/* Back to Top Button */
.back-to-top {
    position: fixed !important;
    bottom: 2rem !important;
    right: 2rem !important;
    width: 3rem !important;
    height: 3rem !important;
    background-color: #49a75c !important;
    color: white !important;
    border: none !important;
    border-radius: 50% !important;
    cursor: pointer !important;
    opacity: 0 !important;
    visibility: hidden !important;
    transition: all 0.3s ease !important;
    z-index: 50 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.back-to-top.show {
    opacity: 1 !important;
    visibility: visible !important;
}

.back-to-top:hover {
    background-color: #5894d2 !important;
    transform: translateY(-2px) !important;
}

/* Form Styles */
.form-group {
    margin-bottom: 1.25rem !important;
}

.form-label {
    display: block !important;
    margin-bottom: 0.25rem !important;
    font-weight: 500 !important;
    color: #1f2937 !important;
}

.form-control {
    width: 100% !important;
    padding: 0.75rem 1rem !important;
    border: 2px solid #e5e7eb !important;
    border-radius: 0.375rem !important;
    font-size: 1rem !important;
    transition: all 0.3s ease !important;
}

.form-control:focus {
    outline: none !important;
    border-color: #49a75c !important;
    box-shadow: 0 0 0 3px rgba(73, 167, 92, 0.1) !important;
}

.form-control.error {
    border-color: #ef4444 !important;
}

.field-error {
    color: #ef4444 !important;
    font-size: 0.875rem !important;
    margin-top: 0.25rem !important;
}

/* Utility Classes */
.img-fluid {
    max-width: 100% !important;
    height: auto !important;
}

.text-shadow {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3) !important;
}

.gradient-overlay {
    background: linear-gradient(135deg, rgba(73, 167, 92, 0.8), rgba(88, 148, 210, 0.6)) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .top-bar-content {
        flex-direction: column !important;
        gap: 0.5rem !important;
        text-align: center !important;
    }

    .contact-info span {
        margin: 0 0.5rem !important;
        display: block !important;
        margin-bottom: 0.25rem !important;
    }

    .main-nav {
        display: none !important;
    }

    .header-actions .btn {
        padding: 0.5rem 1rem !important;
        font-size: 0.875rem !important;
    }

    .logo-text h1 {
        font-size: 1.25rem !important;
    }

    .back-to-top {
        bottom: 1.25rem !important;
        right: 1.25rem !important;
        width: 2.75rem !important;
        height: 2.75rem !important;
    }
}
