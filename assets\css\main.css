/**
 * Main Stylesheet
 * Redolence Salon & Spa
 */

/* CSS Variables */
:root {
    --primary-green: #49a75c;
    --primary-blue: #5894d2;
    --background-color: #ffffff;
    --text-dark: #333333;
    --text-light: #666666;
    --accent-color: #f8f9fa;
    --border-color: #e9ecef;
    --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 20px rgba(0, 0, 0, 0.15);
    --shadow-heavy: 0 8px 30px rgba(0, 0, 0, 0.2);
    --border-radius: 8px;
    --transition: all 0.3s ease;
    
    /* Typography */
    --font-primary: 'Poppins', sans-serif;
    --font-secondary: 'Playfair Display', serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-primary);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-dark);
    background-color: var(--background-color);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-secondary);
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 { font-size: var(--font-size-5xl); }
h2 { font-size: var(--font-size-4xl); }
h3 { font-size: var(--font-size-3xl); }
h4 { font-size: var(--font-size-2xl); }
h5 { font-size: var(--font-size-xl); }
h6 { font-size: var(--font-size-lg); }

p {
    margin-bottom: 1rem;
    color: var(--text-light);
}

a {
    color: var(--primary-green);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--primary-blue);
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 12px 24px;
    font-size: var(--font-size-base);
    font-weight: 500;
    text-align: center;
    text-decoration: none;
    border: 2px solid transparent;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    line-height: 1;
}

.btn-primary {
    background-color: var(--primary-green);
    color: white;
    border-color: var(--primary-green);
}

.btn-primary:hover {
    background-color: var(--primary-blue);
    border-color: var(--primary-blue);
    color: white;
}

.btn-outline {
    background-color: transparent;
    color: var(--primary-green);
    border-color: var(--primary-green);
}

.btn-outline:hover {
    background-color: var(--primary-green);
    color: white;
}

.btn-secondary {
    background-color: var(--primary-blue);
    color: white;
    border-color: var(--primary-blue);
}

.btn-secondary:hover {
    background-color: var(--primary-green);
    border-color: var(--primary-green);
    color: white;
}

/* Top Bar */
.top-bar {
    background-color: var(--text-dark);
    color: white;
    padding: 8px 0;
    font-size: var(--font-size-sm);
}

.top-bar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.contact-info span {
    margin-right: 20px;
}

.contact-info i {
    margin-right: 5px;
    color: var(--primary-green);
}

.social-links a {
    color: white;
    margin-left: 10px;
    font-size: var(--font-size-sm);
    transition: var(--transition);
}

.social-links a:hover {
    color: var(--primary-green);
}

/* Main Header */
.main-header {
    background-color: white;
    box-shadow: var(--shadow-light);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 0;
}

/* Logo */
.logo a {
    display: flex;
    align-items: center;
    text-decoration: none;
}

.logo-img {
    height: 50px;
    margin-right: 15px;
}

.logo-text h1 {
    font-size: var(--font-size-2xl);
    color: var(--primary-green);
    margin: 0;
}

.logo-text span {
    font-size: var(--font-size-sm);
    color: var(--text-light);
    font-style: italic;
}

/* Navigation */
.main-nav {
    flex: 1;
    margin: 0 40px;
}

.nav-menu {
    display: flex;
    list-style: none;
    justify-content: center;
}

.nav-menu li {
    position: relative;
    margin: 0 15px;
}

.nav-menu a {
    color: var(--text-dark);
    font-weight: 500;
    padding: 10px 0;
    display: block;
    transition: var(--transition);
}

.nav-menu a:hover,
.nav-menu a.active {
    color: var(--primary-green);
}

/* Dropdown */
.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    box-shadow: var(--shadow-medium);
    border-radius: var(--border-radius);
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition);
    z-index: 1000;
}

.dropdown-menu li {
    margin: 0;
}

.dropdown-menu a {
    padding: 12px 20px;
    border-bottom: 1px solid var(--border-color);
}

.dropdown-menu a:hover {
    background-color: var(--accent-color);
}

/* Header Actions */
.header-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.user-menu {
    position: relative;
}

.user-toggle {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-dark);
    font-weight: 500;
}

/* Mobile Menu */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background-color: var(--text-dark);
    margin: 3px 0;
    transition: var(--transition);
}

.mobile-nav {
    display: none;
    background-color: white;
    box-shadow: var(--shadow-light);
}

.mobile-nav-menu {
    list-style: none;
    padding: 20px;
}

.mobile-nav-menu li {
    margin: 10px 0;
}

.mobile-nav-menu a {
    color: var(--text-dark);
    font-weight: 500;
    padding: 10px 0;
    display: block;
    border-bottom: 1px solid var(--border-color);
}

/* Flash Messages */
.flash-message {
    padding: 15px 0;
    margin-bottom: 0;
    position: relative;
}

.flash-message .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.flash-info { background-color: #d1ecf1; color: #0c5460; }
.flash-success { background-color: #d4edda; color: #155724; }
.flash-warning { background-color: #fff3cd; color: #856404; }
.flash-error { background-color: #f8d7da; color: #721c24; }

.flash-close {
    background: none;
    border: none;
    font-size: var(--font-size-xl);
    cursor: pointer;
    color: inherit;
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background-color: var(--primary-green);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.back-to-top.show {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background-color: var(--primary-blue);
    transform: translateY(-2px);
}

/* Notifications */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: var(--border-radius);
    color: white;
    z-index: 1001;
    transform: translateX(100%);
    transition: var(--transition);
    max-width: 300px;
}

.notification.show {
    transform: translateX(0);
}

.notification-info { background-color: var(--primary-blue); }
.notification-success { background-color: var(--primary-green); }
.notification-warning { background-color: #ffc107; }
.notification-error { background-color: #dc3545; }

.notification-close {
    background: none;
    border: none;
    color: white;
    font-size: var(--font-size-lg);
    cursor: pointer;
    float: right;
    margin-left: 10px;
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: var(--text-dark);
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: var(--font-size-base);
    transition: var(--transition);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-green);
    box-shadow: 0 0 0 3px rgba(73, 167, 92, 0.1);
}

.form-control.error {
    border-color: #dc3545;
}

.field-error {
    color: #dc3545;
    font-size: var(--font-size-sm);
    margin-top: 5px;
}

.input-group {
    display: flex;
}

.input-group .form-control {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.input-group .btn {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.img-fluid {
    max-width: 100%;
    height: auto;
}

.section-header {
    margin-bottom: 50px;
}

.section-subtitle {
    font-size: var(--font-size-lg);
    color: var(--text-light);
    margin-top: -10px;
}

.btn-lg {
    padding: 15px 30px;
    font-size: var(--font-size-lg);
}

/* Responsive Design */
@media (max-width: 768px) {
    .top-bar-content {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }

    .contact-info span {
        margin: 0 10px;
        display: block;
        margin-bottom: 5px;
    }

    .main-nav {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .header-actions .btn {
        padding: 8px 16px;
        font-size: var(--font-size-sm);
    }

    .logo-text h1 {
        font-size: var(--font-size-xl);
    }

    .back-to-top {
        bottom: 20px;
        right: 20px;
        width: 45px;
        height: 45px;
    }

    .notification {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
    }
}
