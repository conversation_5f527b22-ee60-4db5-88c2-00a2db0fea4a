/**
 * Luxury Salon & Spa Stylesheet with Tailwind CSS
 * Redolence Salon & Spa - Premium Design
 */

@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Custom Base Styles */
@layer base {
  html {
    scroll-behavior: smooth;
    font-size: 16px;
  }

  body {
    @apply font-primary text-base leading-relaxed text-gray-800 bg-white antialiased;
    font-feature-settings: "kern" 1, "liga" 1;
    text-rendering: optimizeLegibility;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-secondary font-semibold leading-tight mb-6 text-gray-900;
    letter-spacing: -0.025em;
  }

  h1 {
    @apply text-5xl lg:text-6xl xl:text-7xl;
    line-height: 1.1;
  }
  h2 {
    @apply text-4xl lg:text-5xl xl:text-6xl;
    line-height: 1.15;
  }
  h3 {
    @apply text-3xl lg:text-4xl;
    line-height: 1.2;
  }
  h4 {
    @apply text-2xl lg:text-3xl;
    line-height: 1.25;
  }
  h5 {
    @apply text-xl lg:text-2xl;
    line-height: 1.3;
  }
  h6 {
    @apply text-lg lg:text-xl;
    line-height: 1.35;
  }

  p {
    @apply mb-6 text-gray-600 leading-relaxed;
    font-size: 1.125rem;
    line-height: 1.7;
  }

  a {
    @apply text-primary-green no-underline transition-all duration-300 ease-out;
  }

  a:hover {
    @apply text-primary-blue;
  }

  /* Luxury scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-primary-green rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-primary-blue;
  }
}

/* Luxury Components */
@layer components {
  /* Container */
  .container {
    @apply max-w-7xl mx-auto px-6 lg:px-8;
  }

  /* Luxury Buttons */
  .btn {
    @apply inline-flex items-center justify-center px-8 py-4 text-base font-semibold text-center no-underline border-2 border-transparent rounded-xl cursor-pointer transition-all duration-500 ease-out leading-none relative overflow-hidden;
    letter-spacing: 0.025em;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  }

  .btn::before {
    content: '';
    @apply absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent transform -skew-x-12 -translate-x-full transition-transform duration-700;
  }

  .btn:hover::before {
    @apply translate-x-full;
  }

  .btn-primary {
    @apply bg-gradient-to-r from-primary-green to-primary-green/90 text-white border-primary-green shadow-lg shadow-primary-green/25;
  }

  .btn-primary:hover {
    @apply bg-gradient-to-r from-primary-blue to-primary-blue/90 border-primary-blue shadow-xl shadow-primary-blue/30 -translate-y-1;
  }

  .btn-outline {
    @apply bg-transparent text-primary-green border-primary-green backdrop-blur-sm;
  }

  .btn-outline:hover {
    @apply bg-primary-green text-white shadow-lg shadow-primary-green/25 -translate-y-1;
  }

  .btn-secondary {
    @apply bg-gradient-to-r from-primary-blue to-primary-blue/90 text-white border-primary-blue shadow-lg shadow-primary-blue/25;
  }

  .btn-secondary:hover {
    @apply bg-gradient-to-r from-primary-green to-primary-green/90 border-primary-green shadow-xl shadow-primary-green/30 -translate-y-1;
  }

  .btn-lg {
    @apply px-12 py-5 text-lg rounded-2xl;
  }

  .btn-sm {
    @apply px-6 py-3 text-sm rounded-lg;
  }

  .btn-ghost {
    @apply bg-white/10 text-white border-white/30 backdrop-blur-md;
  }

  .btn-ghost:hover {
    @apply bg-white/20 border-white/50 -translate-y-1;
  }

  /* Luxury Top Bar */
  .top-bar {
    @apply bg-gradient-to-r from-gray-900 to-gray-800 text-white py-3 text-sm border-b border-gray-700/50;
    backdrop-filter: blur(10px);
  }

  .top-bar-content {
    @apply flex justify-between items-center;
  }

  .contact-info {
    @apply flex items-center space-x-6;
  }

  .contact-info span {
    @apply flex items-center space-x-2 text-gray-300;
  }

  .contact-info i {
    @apply text-primary-green text-sm;
  }

  .social-links {
    @apply flex items-center space-x-3;
  }

  .social-links a {
    @apply w-8 h-8 flex items-center justify-center rounded-full bg-white/10 text-white text-sm transition-all duration-300 hover:bg-primary-green hover:text-white hover:scale-110;
  }

  /* Luxury Main Header */
  .main-header {
    @apply bg-white/95 backdrop-blur-lg shadow-xl sticky top-0 z-50 border-b border-gray-100;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  }

  .header-content {
    @apply flex items-center justify-between py-6;
  }

  /* Luxury Logo */
  .logo a {
    @apply flex items-center no-underline group;
  }

  .logo-img {
    @apply h-14 mr-4 transition-transform duration-300 group-hover:scale-105;
  }

  .logo-text h1 {
    @apply text-3xl text-primary-green m-0 font-bold tracking-tight;
    background: linear-gradient(135deg, #49a75c, #5894d2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .logo-text span {
    @apply text-sm text-gray-500 italic font-light tracking-wide;
  }

  /* Luxury Navigation */
  .main-nav {
    @apply flex-1 mx-12;
  }

  .nav-menu {
    @apply flex list-none justify-center space-x-8;
  }

  .nav-menu li {
    @apply relative;
  }

  .nav-menu a {
    @apply text-gray-700 font-medium py-3 px-4 block transition-all duration-300 relative overflow-hidden rounded-lg;
    letter-spacing: 0.025em;
  }

  .nav-menu a::before {
    content: '';
    @apply absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-primary-green to-primary-blue transition-all duration-300;
  }

  .nav-menu a:hover::before,
  .nav-menu a.active::before {
    @apply w-full;
  }

  .nav-menu a:hover,
  .nav-menu a.active {
    @apply text-primary-green bg-primary-green/5;
  }

  /* Luxury Dropdown */
  .dropdown:hover .dropdown-menu {
    @apply opacity-100 visible translate-y-0;
  }

  .dropdown-menu {
    @apply absolute top-full left-0 bg-white/95 backdrop-blur-lg shadow-2xl rounded-2xl min-w-[240px] opacity-0 invisible -translate-y-4 transition-all duration-500 z-50 border border-gray-100 overflow-hidden;
  }

  .dropdown-menu li {
    @apply m-0;
  }

  .dropdown-menu a {
    @apply px-6 py-4 border-b border-gray-100/50 text-gray-600 hover:text-primary-green hover:bg-gradient-to-r hover:from-primary-green/5 hover:to-primary-blue/5 transition-all duration-300;
  }

  .dropdown-menu a:last-child {
    @apply border-b-0;
  }

  /* Header Actions */
  .header-actions {
    @apply flex items-center gap-4;
  }

  .user-menu {
    @apply relative;
  }

  .user-toggle {
    @apply flex items-center gap-2 text-text-dark font-medium;
  }

  /* Mobile Menu */
  .mobile-menu-toggle {
    @apply hidden flex-col bg-transparent border-none cursor-pointer p-1;
  }

  .mobile-menu-toggle span {
    @apply w-6 h-0.5 bg-text-dark my-0.5 transition-all duration-300;
  }

  .mobile-nav {
    @apply hidden bg-white shadow-light;
  }

  .mobile-nav-menu {
    @apply list-none p-5;
  }

  .mobile-nav-menu li {
    @apply my-2;
  }

  .mobile-nav-menu a {
    @apply text-text-dark font-medium py-2 block border-b border-border;
  }

  /* Flash Messages */
  .flash-message {
    @apply py-4 mb-0 relative;
  }

  .flash-message .container {
    @apply flex justify-between items-center;
  }

  .flash-info { @apply bg-blue-100 text-blue-800; }
  .flash-success { @apply bg-green-100 text-green-800; }
  .flash-warning { @apply bg-yellow-100 text-yellow-800; }
  .flash-error { @apply bg-red-100 text-red-800; }

  .flash-close {
    @apply bg-transparent border-none text-xl cursor-pointer;
  }

  /* Back to Top Button */
  .back-to-top {
    @apply fixed bottom-8 right-8 w-12 h-12 bg-primary-green text-white border-none rounded-full cursor-pointer opacity-0 invisible transition-all duration-300 z-50 flex items-center justify-center;
  }

  .back-to-top.show {
    @apply opacity-100 visible;
  }

  .back-to-top:hover {
    @apply bg-primary-blue -translate-y-0.5;
  }

  /* Notifications */
  .notification {
    @apply fixed top-5 right-5 px-5 py-4 rounded text-white z-[1001] translate-x-full transition-all duration-300 max-w-xs;
  }

  .notification.show {
    @apply translate-x-0;
  }

  .notification-info { @apply bg-primary-blue; }
  .notification-success { @apply bg-primary-green; }
  .notification-warning { @apply bg-yellow-500; }
  .notification-error { @apply bg-red-500; }

  .notification-close {
    @apply bg-transparent border-none text-white text-lg cursor-pointer float-right ml-2;
  }

  /* Form Styles */
  .form-group {
    @apply mb-5;
  }

  .form-label {
    @apply block mb-1 font-medium text-text-dark;
  }

  .form-control {
    @apply w-full px-4 py-3 border-2 border-border rounded text-base transition-all duration-300;
  }

  .form-control:focus {
    @apply outline-none border-primary-green shadow-[0_0_0_3px_rgba(73,167,92,0.1)];
  }

  .form-control.error {
    @apply border-red-500;
  }

  .field-error {
    @apply text-red-500 text-sm mt-1;
  }

  .input-group {
    @apply flex;
  }

  .input-group .form-control {
    @apply rounded-r-none;
  }

  .input-group .btn {
    @apply rounded-l-none;
  }

  /* Utility Classes */
  .section-header {
    @apply mb-12;
  }

  .section-subtitle {
    @apply text-lg text-text-light -mt-2;
  }
}

/* Custom Utilities */
@layer utilities {
  .img-fluid {
    @apply max-w-full h-auto;
  }

  .text-shadow {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  }

  .gradient-overlay {
    background: linear-gradient(135deg, rgba(73, 167, 92, 0.8), rgba(88, 148, 210, 0.6));
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .top-bar-content {
    @apply flex-col gap-2 text-center;
  }

  .contact-info span {
    @apply mx-2 block mb-1;
  }

  .main-nav {
    @apply hidden;
  }

  .mobile-menu-toggle {
    @apply flex;
  }

  .header-actions .btn {
    @apply px-4 py-2 text-sm;
  }

  .logo-text h1 {
    @apply text-xl;
  }

  .back-to-top {
    @apply bottom-5 right-5 w-11 h-11;
  }

  .notification {
    @apply top-2 right-2 left-2 max-w-none;
  }
}
