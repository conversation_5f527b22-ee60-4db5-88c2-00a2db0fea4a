<?php
/**
 * Header Component
 * Redolence Salon & Spa
 */

require_once __DIR__ . '/../database.php';
require_once __DIR__ . '/../auth.php';

startSession();
$currentUser = getCurrentUser();
$currentPage = basename($_SERVER['PHP_SELF'], '.php');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="<?php echo $pageDescription ?? 'Redolence Salon & Spa - Your Beauty, Our Passion. Professional beauty and wellness services in Dar es Salaam.'; ?>">
    <meta name="keywords" content="salon, spa, beauty, wellness, massage, facial, hair, nails, Dar es Salaam, Tanzania">
    <meta name="author" content="Redolence Salon & Spa">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo $pageTitle ?? APP_NAME; ?>">
    <meta property="og:description" content="<?php echo $pageDescription ?? APP_TAGLINE; ?>">
    <meta property="og:image" content="<?php echo ASSETS_URL; ?>/images/og-image.jpg">
    <meta property="og:url" content="<?php echo BASE_URL . $_SERVER['REQUEST_URI']; ?>">
    <meta property="og:type" content="website">
    
    <title><?php echo isset($pageTitle) ? $pageTitle . ' - ' . APP_NAME : APP_NAME . ' - ' . APP_TAGLINE; ?></title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo ASSETS_URL; ?>/images/favicon.ico">
    <link rel="apple-touch-icon" href="<?php echo ASSETS_URL; ?>/images/apple-touch-icon.png">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Tailwind Configuration -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary-green': '#49a75c',
                        'primary-blue': '#5894d2',
                        'text-dark': '#333333',
                        'text-light': '#666666',
                        'accent': '#f8f9fa',
                        'border': '#e9ecef',
                    },
                    fontFamily: {
                        'primary': ['Poppins', 'sans-serif'],
                        'secondary': ['Playfair Display', 'serif'],
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-up': 'slideUp 0.5s ease-out',
                        'bounce': 'bounce 2s infinite',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' },
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(20px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' },
                        }
                    }
                }
            }
        }
    </script>

    <!-- Custom CSS - MUST come after Tailwind -->
    <link href="<?php echo ASSETS_URL; ?>/css/main.css" rel="stylesheet">

    <!-- Page-specific CSS -->
    <?php if (isset($additionalCSS) && is_array($additionalCSS)): ?>
        <?php foreach ($additionalCSS as $css): ?>
            <link href="<?php echo $css; ?>" rel="stylesheet">
        <?php endforeach; ?>
    <?php endif; ?>
    
    <!-- Additional CSS -->
    <?php if (isset($additionalCSS)): ?>
        <?php foreach ($additionalCSS as $css): ?>
            <link href="<?php echo $css; ?>" rel="stylesheet">
        <?php endforeach; ?>
    <?php endif; ?>
</head>
<body class="<?php echo $bodyClass ?? ''; ?>">
    
    <!-- Luxury Top Bar -->
    <div class="top-bar">
        <div class="container">
            <div class="top-bar-content">
                <div class="contact-info">
                    <span><i class="fas fa-phone"></i> <?php echo BUSINESS_PHONE; ?></span>
                    <span><i class="fas fa-envelope"></i> <?php echo BUSINESS_EMAIL; ?></span>
                    <span class="hidden lg:flex"><i class="fas fa-clock"></i> <?php echo BUSINESS_HOURS; ?></span>
                </div>
                <div class="social-links">
                    <a href="<?php echo SOCIAL_FACEBOOK; ?>" target="_blank" aria-label="Facebook"><i class="fab fa-facebook-f"></i></a>
                    <a href="<?php echo SOCIAL_INSTAGRAM; ?>" target="_blank" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                    <a href="<?php echo SOCIAL_TWITTER; ?>" target="_blank" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
                    <a href="<?php echo SOCIAL_YOUTUBE; ?>" target="_blank" aria-label="YouTube"><i class="fab fa-youtube"></i></a>
                </div>
            </div>
        </div>
    </div>

    <!-- Luxury Main Header -->
    <header class="main-header">
        <div class="container">
            <div class="header-content">
                <!-- Luxury Logo -->
                <div class="logo">
                    <a href="<?php echo BASE_URL; ?>">
                        <img src="<?php echo ASSETS_URL; ?>/images/logo.png" alt="<?php echo APP_NAME; ?>" class="logo-img">
                        <div class="logo-text">
                            <h1><?php echo APP_NAME; ?></h1>
                            <span><?php echo APP_TAGLINE; ?></span>
                        </div>
                    </a>
                </div>

                <!-- Luxury Navigation -->
                <nav class="main-nav hidden lg:block">
                    <ul class="nav-menu">
                        <li><a href="<?php echo BASE_URL; ?>" class="<?php echo $currentPage === 'index' ? 'active' : ''; ?>">Home</a></li>
                        <li><a href="<?php echo BASE_URL; ?>/about" class="<?php echo $currentPage === 'about' ? 'active' : ''; ?>">About</a></li>
                        <li class="dropdown">
                            <a href="<?php echo BASE_URL; ?>/services" class="<?php echo $currentPage === 'services' ? 'active' : ''; ?>">Services <i class="fas fa-chevron-down ml-2 text-xs"></i></a>
                            <ul class="dropdown-menu">
                                <li><a href="<?php echo BASE_URL; ?>/services?category=hair"><i class="fas fa-cut mr-3 text-primary-green"></i>Hair Services</a></li>
                                <li><a href="<?php echo BASE_URL; ?>/services?category=facial"><i class="fas fa-leaf mr-3 text-primary-green"></i>Facial Treatments</a></li>
                                <li><a href="<?php echo BASE_URL; ?>/services?category=massage"><i class="fas fa-spa mr-3 text-primary-green"></i>Massage Therapy</a></li>
                                <li><a href="<?php echo BASE_URL; ?>/services?category=nails"><i class="fas fa-hand-sparkles mr-3 text-primary-green"></i>Nail Care</a></li>
                                <li><a href="<?php echo BASE_URL; ?>/services?category=body"><i class="fas fa-heart mr-3 text-primary-green"></i>Body Treatments</a></li>
                            </ul>
                        </li>
                        <li><a href="<?php echo BASE_URL; ?>/packages" class="<?php echo $currentPage === 'packages' ? 'active' : ''; ?>">Packages</a></li>
                        <li><a href="<?php echo BASE_URL; ?>/gallery" class="<?php echo $currentPage === 'gallery' ? 'active' : ''; ?>">Gallery</a></li>
                        <li><a href="<?php echo BASE_URL; ?>/contact" class="<?php echo $currentPage === 'contact' ? 'active' : ''; ?>">Contact</a></li>
                    </ul>
                </nav>

                <!-- Luxury Header Actions -->
                <div class="header-actions">
                    <?php if ($currentUser): ?>
                        <div class="user-menu dropdown">
                            <a href="#" class="user-toggle">
                                <i class="fas fa-user"></i>
                                <span><?php echo htmlspecialchars($currentUser['first_name'] ?? $currentUser['name']); ?></span>
                                <i class="fas fa-chevron-down"></i>
                            </a>
                            <ul class="dropdown-menu">
                                <?php if (hasRole('customer')): ?>
                                    <li><a href="<?php echo BASE_URL; ?>/customer"><i class="fas fa-tachometer-alt mr-3 text-primary-green"></i> Dashboard</a></li>
                                    <li><a href="<?php echo BASE_URL; ?>/customer/bookings"><i class="fas fa-calendar mr-3 text-primary-green"></i> My Bookings</a></li>
                                    <li><a href="<?php echo BASE_URL; ?>/customer/profile"><i class="fas fa-user-edit mr-3 text-primary-green"></i> Profile</a></li>
                                <?php elseif (hasRole('admin')): ?>
                                    <li><a href="<?php echo BASE_URL; ?>/admin"><i class="fas fa-cog mr-3 text-primary-green"></i> Admin Panel</a></li>
                                <?php elseif (hasRole('staff')): ?>
                                    <li><a href="<?php echo BASE_URL; ?>/staff"><i class="fas fa-briefcase mr-3 text-primary-green"></i> Staff Panel</a></li>
                                <?php endif; ?>
                                <li><a href="<?php echo BASE_URL; ?>/auth/logout"><i class="fas fa-sign-out-alt mr-3 text-primary-green"></i> Logout</a></li>
                            </ul>
                        </div>
                    <?php else: ?>
                        <a href="<?php echo BASE_URL; ?>/auth/login" class="btn btn-outline btn-sm">
                            <i class="fas fa-sign-in-alt mr-2"></i>
                            Login
                        </a>
                    <?php endif; ?>

                    <a href="<?php echo BASE_URL; ?>/contact" class="btn btn-primary">
                        <i class="fas fa-calendar-plus mr-2"></i>
                        Book Now
                    </a>
                </div>

                <!-- Mobile Menu Toggle -->
                <button class="mobile-menu-toggle lg:hidden flex flex-col bg-transparent border-none cursor-pointer p-1" aria-label="Toggle mobile menu">
                    <span class="w-6 h-0.5 bg-text-dark my-0.5 transition-all duration-300"></span>
                    <span class="w-6 h-0.5 bg-text-dark my-0.5 transition-all duration-300"></span>
                    <span class="w-6 h-0.5 bg-text-dark my-0.5 transition-all duration-300"></span>
                </button>
            </div>
        </div>
    </header>

    <!-- Mobile Navigation -->
    <nav class="mobile-nav hidden bg-white shadow-lg lg:hidden">
        <ul class="list-none p-5">
            <li class="my-2"><a href="<?php echo BASE_URL; ?>" class="text-text-dark font-medium py-2 block border-b border-border transition-colors duration-300 hover:text-primary-green">Home</a></li>
            <li class="my-2"><a href="<?php echo BASE_URL; ?>/about" class="text-text-dark font-medium py-2 block border-b border-border transition-colors duration-300 hover:text-primary-green">About</a></li>
            <li class="my-2"><a href="<?php echo BASE_URL; ?>/services" class="text-text-dark font-medium py-2 block border-b border-border transition-colors duration-300 hover:text-primary-green">Services</a></li>
            <li class="my-2"><a href="<?php echo BASE_URL; ?>/packages" class="text-text-dark font-medium py-2 block border-b border-border transition-colors duration-300 hover:text-primary-green">Packages</a></li>
            <li class="my-2"><a href="<?php echo BASE_URL; ?>/gallery" class="text-text-dark font-medium py-2 block border-b border-border transition-colors duration-300 hover:text-primary-green">Gallery</a></li>
            <li class="my-2"><a href="<?php echo BASE_URL; ?>/contact" class="text-text-dark font-medium py-2 block border-b border-border transition-colors duration-300 hover:text-primary-green">Contact</a></li>
            <?php if ($currentUser): ?>
                <li class="my-2"><a href="<?php echo BASE_URL; ?>/customer" class="text-text-dark font-medium py-2 block border-b border-border transition-colors duration-300 hover:text-primary-green">Dashboard</a></li>
                <li class="my-2"><a href="<?php echo BASE_URL; ?>/auth/logout" class="text-text-dark font-medium py-2 block border-b border-border transition-colors duration-300 hover:text-primary-green">Logout</a></li>
            <?php else: ?>
                <li class="my-2"><a href="<?php echo BASE_URL; ?>/auth/login" class="text-text-dark font-medium py-2 block border-b border-border transition-colors duration-300 hover:text-primary-green">Login</a></li>
            <?php endif; ?>
        </ul>
    </nav>

    <!-- Flash Messages -->
    <?php
    $flashMessage = getFlashMessage();
    if ($flashMessage):
    ?>
        <div class="py-4 mb-0 relative flash-<?php echo $flashMessage['type']; ?>">
            <div class="container">
                <div class="flex justify-between items-center">
                    <span><?php echo htmlspecialchars($flashMessage['message']); ?></span>
                    <button class="bg-transparent border-none text-xl cursor-pointer flash-close">&times;</button>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Main Content -->
    <main class="main-content"><?php // Content will be inserted here ?>
