<?php
/**
 * Header Component
 * Redolence Salon & Spa
 */

require_once __DIR__ . '/../database.php';
require_once __DIR__ . '/../auth.php';

startSession();
$currentUser = getCurrentUser();
$currentPage = basename($_SERVER['PHP_SELF'], '.php');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="<?php echo $pageDescription ?? 'Redolence Salon & Spa - Your Beauty, Our Passion. Professional beauty and wellness services in Dar es Salaam.'; ?>">
    <meta name="keywords" content="salon, spa, beauty, wellness, massage, facial, hair, nails, Dar es Salaam, Tanzania">
    <meta name="author" content="Redolence Salon & Spa">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo $pageTitle ?? APP_NAME; ?>">
    <meta property="og:description" content="<?php echo $pageDescription ?? APP_TAGLINE; ?>">
    <meta property="og:image" content="<?php echo ASSETS_URL; ?>/images/og-image.jpg">
    <meta property="og:url" content="<?php echo BASE_URL . $_SERVER['REQUEST_URI']; ?>">
    <meta property="og:type" content="website">
    
    <title><?php echo isset($pageTitle) ? $pageTitle . ' - ' . APP_NAME : APP_NAME . ' - ' . APP_TAGLINE; ?></title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo ASSETS_URL; ?>/images/favicon.ico">
    <link rel="apple-touch-icon" href="<?php echo ASSETS_URL; ?>/images/apple-touch-icon.png">
    
    <!-- CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="<?php echo ASSETS_URL; ?>/css/main.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary-green': '#49a75c',
                        'primary-blue': '#5894d2',
                        'text-dark': '#333333',
                        'text-light': '#666666',
                        'accent': '#f8f9fa',
                        'border': '#e9ecef',
                    },
                    fontFamily: {
                        'primary': ['Poppins', 'sans-serif'],
                        'secondary': ['Playfair Display', 'serif'],
                    }
                }
            }
        }
    </script>
    
    <!-- Additional CSS -->
    <?php if (isset($additionalCSS)): ?>
        <?php foreach ($additionalCSS as $css): ?>
            <link href="<?php echo $css; ?>" rel="stylesheet">
        <?php endforeach; ?>
    <?php endif; ?>
</head>
<body class="<?php echo $bodyClass ?? ''; ?>">
    
    <!-- Top Bar -->
    <div class="bg-text-dark text-white py-2 text-sm">
        <div class="container">
            <div class="flex flex-col md:flex-row justify-between items-center gap-2 md:gap-0">
                <div class="contact-info flex flex-wrap justify-center md:justify-start gap-4">
                    <span><i class="fas fa-phone mr-1 text-primary-green"></i> <?php echo BUSINESS_PHONE; ?></span>
                    <span><i class="fas fa-envelope mr-1 text-primary-green"></i> <?php echo BUSINESS_EMAIL; ?></span>
                    <span class="hidden lg:inline"><i class="fas fa-clock mr-1 text-primary-green"></i> <?php echo BUSINESS_HOURS; ?></span>
                </div>
                <div class="social-links flex gap-2">
                    <a href="<?php echo SOCIAL_FACEBOOK; ?>" target="_blank" aria-label="Facebook" class="text-white hover:text-primary-green transition-colors duration-300"><i class="fab fa-facebook-f"></i></a>
                    <a href="<?php echo SOCIAL_INSTAGRAM; ?>" target="_blank" aria-label="Instagram" class="text-white hover:text-primary-green transition-colors duration-300"><i class="fab fa-instagram"></i></a>
                    <a href="<?php echo SOCIAL_TWITTER; ?>" target="_blank" aria-label="Twitter" class="text-white hover:text-primary-green transition-colors duration-300"><i class="fab fa-twitter"></i></a>
                    <a href="<?php echo SOCIAL_YOUTUBE; ?>" target="_blank" aria-label="YouTube" class="text-white hover:text-primary-green transition-colors duration-300"><i class="fab fa-youtube"></i></a>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Header -->
    <header class="bg-white shadow-lg sticky top-0 z-50">
        <div class="container">
            <div class="flex items-center justify-between py-4">
                <!-- Logo -->
                <div class="logo">
                    <a href="<?php echo BASE_URL; ?>" class="flex items-center no-underline">
                        <img src="<?php echo ASSETS_URL; ?>/images/logo.png" alt="<?php echo APP_NAME; ?>" class="h-12 mr-4">
                        <div class="logo-text">
                            <h1 class="text-2xl text-primary-green m-0 font-secondary"><?php echo APP_NAME; ?></h1>
                            <span class="text-sm text-text-light italic"><?php echo APP_TAGLINE; ?></span>
                        </div>
                    </a>
                </div>

                <!-- Navigation -->
                <nav class="main-nav hidden lg:block flex-1 mx-10">
                    <ul class="flex list-none justify-center">
                        <li class="mx-4"><a href="<?php echo BASE_URL; ?>" class="text-text-dark font-medium py-2 block transition-colors duration-300 hover:text-primary-green <?php echo $currentPage === 'index' ? 'text-primary-green' : ''; ?>">Home</a></li>
                        <li class="mx-4"><a href="<?php echo BASE_URL; ?>/about" class="text-text-dark font-medium py-2 block transition-colors duration-300 hover:text-primary-green <?php echo $currentPage === 'about' ? 'text-primary-green' : ''; ?>">About</a></li>
                        <li class="mx-4 relative group">
                            <a href="<?php echo BASE_URL; ?>/services" class="text-text-dark font-medium py-2 block transition-colors duration-300 hover:text-primary-green <?php echo $currentPage === 'services' ? 'text-primary-green' : ''; ?>">Services <i class="fas fa-chevron-down ml-1"></i></a>
                            <ul class="absolute top-full left-0 bg-white shadow-lg rounded min-w-[200px] opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <li><a href="<?php echo BASE_URL; ?>/services?category=hair" class="block px-5 py-3 text-text-dark hover:bg-accent transition-colors duration-300">Hair Services</a></li>
                                <li><a href="<?php echo BASE_URL; ?>/services?category=facial" class="block px-5 py-3 text-text-dark hover:bg-accent transition-colors duration-300">Facial Treatments</a></li>
                                <li><a href="<?php echo BASE_URL; ?>/services?category=massage" class="block px-5 py-3 text-text-dark hover:bg-accent transition-colors duration-300">Massage Therapy</a></li>
                                <li><a href="<?php echo BASE_URL; ?>/services?category=nails" class="block px-5 py-3 text-text-dark hover:bg-accent transition-colors duration-300">Nail Care</a></li>
                                <li><a href="<?php echo BASE_URL; ?>/services?category=body" class="block px-5 py-3 text-text-dark hover:bg-accent transition-colors duration-300 border-b-0">Body Treatments</a></li>
                            </ul>
                        </li>
                        <li class="mx-4"><a href="<?php echo BASE_URL; ?>/packages" class="text-text-dark font-medium py-2 block transition-colors duration-300 hover:text-primary-green <?php echo $currentPage === 'packages' ? 'text-primary-green' : ''; ?>">Packages</a></li>
                        <li class="mx-4"><a href="<?php echo BASE_URL; ?>/gallery" class="text-text-dark font-medium py-2 block transition-colors duration-300 hover:text-primary-green <?php echo $currentPage === 'gallery' ? 'text-primary-green' : ''; ?>">Gallery</a></li>
                        <li class="mx-4"><a href="<?php echo BASE_URL; ?>/contact" class="text-text-dark font-medium py-2 block transition-colors duration-300 hover:text-primary-green <?php echo $currentPage === 'contact' ? 'text-primary-green' : ''; ?>">Contact</a></li>
                    </ul>
                </nav>

                <!-- Header Actions -->
                <div class="flex items-center gap-4">
                    <?php if ($currentUser): ?>
                        <div class="user-menu relative group">
                            <a href="#" class="flex items-center gap-2 text-text-dark font-medium">
                                <i class="fas fa-user"></i>
                                <span><?php echo htmlspecialchars($currentUser['first_name'] ?? $currentUser['name']); ?></span>
                                <i class="fas fa-chevron-down"></i>
                            </a>
                            <ul class="absolute top-full right-0 bg-white shadow-lg rounded min-w-[200px] opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                <?php if (hasRole('customer')): ?>
                                    <li><a href="<?php echo BASE_URL; ?>/customer" class="block px-5 py-3 text-text-dark hover:bg-accent transition-colors duration-300"><i class="fas fa-tachometer-alt mr-2"></i> Dashboard</a></li>
                                    <li><a href="<?php echo BASE_URL; ?>/customer/bookings" class="block px-5 py-3 text-text-dark hover:bg-accent transition-colors duration-300"><i class="fas fa-calendar mr-2"></i> My Bookings</a></li>
                                    <li><a href="<?php echo BASE_URL; ?>/customer/profile" class="block px-5 py-3 text-text-dark hover:bg-accent transition-colors duration-300"><i class="fas fa-user-edit mr-2"></i> Profile</a></li>
                                <?php elseif (hasRole('admin')): ?>
                                    <li><a href="<?php echo BASE_URL; ?>/admin" class="block px-5 py-3 text-text-dark hover:bg-accent transition-colors duration-300"><i class="fas fa-cog mr-2"></i> Admin Panel</a></li>
                                <?php elseif (hasRole('staff')): ?>
                                    <li><a href="<?php echo BASE_URL; ?>/staff" class="block px-5 py-3 text-text-dark hover:bg-accent transition-colors duration-300"><i class="fas fa-briefcase mr-2"></i> Staff Panel</a></li>
                                <?php endif; ?>
                                <li><a href="<?php echo BASE_URL; ?>/auth/logout" class="block px-5 py-3 text-text-dark hover:bg-accent transition-colors duration-300 border-b-0"><i class="fas fa-sign-out-alt mr-2"></i> Logout</a></li>
                            </ul>
                        </div>
                    <?php else: ?>
                        <a href="<?php echo BASE_URL; ?>/auth/login" class="btn btn-outline">Login</a>
                    <?php endif; ?>

                    <a href="<?php echo BASE_URL; ?>/contact" class="btn btn-primary">Book Now</a>
                </div>

                <!-- Mobile Menu Toggle -->
                <button class="mobile-menu-toggle lg:hidden flex flex-col bg-transparent border-none cursor-pointer p-1" aria-label="Toggle mobile menu">
                    <span class="w-6 h-0.5 bg-text-dark my-0.5 transition-all duration-300"></span>
                    <span class="w-6 h-0.5 bg-text-dark my-0.5 transition-all duration-300"></span>
                    <span class="w-6 h-0.5 bg-text-dark my-0.5 transition-all duration-300"></span>
                </button>
            </div>
        </div>
    </header>

    <!-- Mobile Navigation -->
    <nav class="mobile-nav hidden bg-white shadow-lg lg:hidden">
        <ul class="list-none p-5">
            <li class="my-2"><a href="<?php echo BASE_URL; ?>" class="text-text-dark font-medium py-2 block border-b border-border transition-colors duration-300 hover:text-primary-green">Home</a></li>
            <li class="my-2"><a href="<?php echo BASE_URL; ?>/about" class="text-text-dark font-medium py-2 block border-b border-border transition-colors duration-300 hover:text-primary-green">About</a></li>
            <li class="my-2"><a href="<?php echo BASE_URL; ?>/services" class="text-text-dark font-medium py-2 block border-b border-border transition-colors duration-300 hover:text-primary-green">Services</a></li>
            <li class="my-2"><a href="<?php echo BASE_URL; ?>/packages" class="text-text-dark font-medium py-2 block border-b border-border transition-colors duration-300 hover:text-primary-green">Packages</a></li>
            <li class="my-2"><a href="<?php echo BASE_URL; ?>/gallery" class="text-text-dark font-medium py-2 block border-b border-border transition-colors duration-300 hover:text-primary-green">Gallery</a></li>
            <li class="my-2"><a href="<?php echo BASE_URL; ?>/contact" class="text-text-dark font-medium py-2 block border-b border-border transition-colors duration-300 hover:text-primary-green">Contact</a></li>
            <?php if ($currentUser): ?>
                <li class="my-2"><a href="<?php echo BASE_URL; ?>/customer" class="text-text-dark font-medium py-2 block border-b border-border transition-colors duration-300 hover:text-primary-green">Dashboard</a></li>
                <li class="my-2"><a href="<?php echo BASE_URL; ?>/auth/logout" class="text-text-dark font-medium py-2 block border-b border-border transition-colors duration-300 hover:text-primary-green">Logout</a></li>
            <?php else: ?>
                <li class="my-2"><a href="<?php echo BASE_URL; ?>/auth/login" class="text-text-dark font-medium py-2 block border-b border-border transition-colors duration-300 hover:text-primary-green">Login</a></li>
            <?php endif; ?>
        </ul>
    </nav>

    <!-- Flash Messages -->
    <?php
    $flashMessage = getFlashMessage();
    if ($flashMessage):
    ?>
        <div class="py-4 mb-0 relative flash-<?php echo $flashMessage['type']; ?>">
            <div class="container">
                <div class="flex justify-between items-center">
                    <span><?php echo htmlspecialchars($flashMessage['message']); ?></span>
                    <button class="bg-transparent border-none text-xl cursor-pointer flash-close">&times;</button>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Main Content -->
    <main class="main-content"><?php // Content will be inserted here ?>
