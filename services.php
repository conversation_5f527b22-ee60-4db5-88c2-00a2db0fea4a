<?php
/**
 * Services Page
 * Redolence Salon & Spa
 */

require_once 'includes/database.php';
require_once 'includes/auth.php';

// Page meta data
$pageTitle = 'Our Services';
$pageDescription = 'Discover our comprehensive range of beauty and wellness services at Redolence Salon & Spa. Professional hair styling, facial treatments, massage therapy, nail care, and body treatments.';
$bodyClass = 'services-page';

// Get category filter
$selectedCategory = $_GET['category'] ?? 'all';

// Sample services data (replace with database query later)
$services = [
    [
        'id' => 1,
        'name' => 'Classic Facial',
        'category' => 'facial',
        'description' => 'Deep cleansing facial treatment for all skin types',
        'duration' => 60,
        'price' => 45000,
        'image' => 'https://images.unsplash.com/photo-1616394584738-fc6e612e71b9?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
    ],
    [
        'id' => 2,
        'name' => 'Swedish Massage',
        'category' => 'massage',
        'description' => 'Relaxing full-body massage to relieve stress and tension',
        'duration' => 90,
        'price' => 65000,
        'image' => 'https://images.unsplash.com/photo-1544161515-4ab6ce6db874?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
    ],
    [
        'id' => 3,
        'name' => 'Hair Cut & Style',
        'category' => 'hair',
        'description' => 'Professional hair cutting and styling service',
        'duration' => 120,
        'price' => 35000,
        'image' => 'https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
    ],
    [
        'id' => 4,
        'name' => 'Manicure & Pedicure',
        'category' => 'nails',
        'description' => 'Complete nail care treatment for hands and feet',
        'duration' => 75,
        'price' => 25000,
        'image' => 'https://images.unsplash.com/photo-1604654894610-df63bc536371?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
    ]
];

// Filter services by category
if ($selectedCategory !== 'all') {
    $services = array_filter($services, function($service) use ($selectedCategory) {
        return $service['category'] === $selectedCategory;
    });
}

$categories = [
    'all' => 'All Services',
    'hair' => 'Hair Services',
    'facial' => 'Facial Treatments',
    'massage' => 'Massage Therapy',
    'nails' => 'Nail Care',
    'body' => 'Body Treatments'
];

include 'includes/components/header.php';
?>

<!-- Page Header -->
<section class="bg-gradient-to-r from-primary-green to-primary-blue text-white py-20">
    <div class="container">
        <div class="text-center">
            <h1 class="text-5xl font-secondary font-bold mb-4">Our Services</h1>
            <p class="text-xl opacity-90 max-w-2xl mx-auto">Discover our comprehensive range of professional beauty and wellness treatments designed to help you look and feel your best.</p>
        </div>
    </div>
</section>

<!-- Services Filter -->
<section class="py-8 bg-accent border-b">
    <div class="container">
        <div class="flex flex-wrap justify-center gap-4">
            <?php foreach ($categories as $key => $name): ?>
                <a href="<?php echo BASE_URL; ?>/services<?php echo $key !== 'all' ? '?category=' . $key : ''; ?>" 
                   class="px-6 py-2 rounded-full border-2 transition-all duration-300 <?php echo $selectedCategory === $key ? 'bg-primary-green text-white border-primary-green' : 'bg-white text-text-dark border-border hover:border-primary-green hover:text-primary-green'; ?>">
                    <?php echo $name; ?>
                </a>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Services Grid -->
<section class="py-20 bg-white">
    <div class="container">
        <?php if (empty($services)): ?>
            <div class="text-center py-20">
                <i class="fas fa-spa text-6xl text-text-light mb-6"></i>
                <h3 class="text-2xl font-secondary text-text-dark mb-4">No services found</h3>
                <p class="text-text-light mb-8">We don't have any services in this category yet.</p>
                <a href="<?php echo BASE_URL; ?>/services" class="btn btn-primary">View All Services</a>
            </div>
        <?php else: ?>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <?php foreach ($services as $service): ?>
                    <div class="bg-white rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                        <!-- Service Image -->
                        <div class="relative h-64 overflow-hidden">
                            <img src="<?php echo $service['image']; ?>" 
                                 alt="<?php echo htmlspecialchars($service['name']); ?>" 
                                 class="w-full h-full object-cover transition-transform duration-300 hover:scale-110">
                            <div class="absolute top-4 right-4">
                                <span class="bg-primary-green text-white px-3 py-1 rounded-full text-sm font-medium">
                                    <?php echo ucfirst($service['category']); ?>
                                </span>
                            </div>
                        </div>
                        
                        <!-- Service Content -->
                        <div class="p-6">
                            <h3 class="text-xl font-secondary font-semibold text-text-dark mb-3">
                                <?php echo htmlspecialchars($service['name']); ?>
                            </h3>
                            <p class="text-text-light mb-4 leading-relaxed">
                                <?php echo htmlspecialchars($service['description']); ?>
                            </p>
                            
                            <!-- Service Meta -->
                            <div class="flex justify-between items-center mb-6">
                                <div class="flex items-center text-text-light">
                                    <i class="fas fa-clock mr-2 text-primary-green"></i>
                                    <span><?php echo $service['duration']; ?> minutes</span>
                                </div>
                                <div class="text-2xl font-bold text-primary-green">
                                    <?php echo number_format($service['price']); ?> TZS
                                </div>
                            </div>
                            
                            <!-- Action Buttons -->
                            <div class="flex gap-3">
                                <a href="<?php echo BASE_URL; ?>/contact?service=<?php echo $service['id']; ?>" 
                                   class="flex-1 btn btn-primary text-center">
                                    Book Now
                                </a>
                                <button class="px-4 py-2 border-2 border-border rounded hover:border-primary-green hover:text-primary-green transition-colors duration-300" 
                                        onclick="showServiceDetails(<?php echo $service['id']; ?>)">
                                    <i class="fas fa-info-circle"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</section>

<!-- Call to Action -->
<section class="py-20 bg-accent">
    <div class="container">
        <div class="text-center max-w-3xl mx-auto">
            <h2 class="text-4xl font-secondary font-semibold text-text-dark mb-6">Ready to Book Your Treatment?</h2>
            <p class="text-lg text-text-light mb-8">Our experienced professionals are here to help you achieve your beauty and wellness goals. Contact us today to schedule your appointment.</p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="<?php echo BASE_URL; ?>/contact" class="btn btn-primary btn-lg">Book Appointment</a>
                <a href="<?php echo BASE_URL; ?>/packages" class="btn btn-outline btn-lg">View Packages</a>
            </div>
        </div>
    </div>
</section>

<!-- Service Details Modal (placeholder) -->
<div id="serviceModal" class="fixed inset-0 bg-black/50 z-50 hidden items-center justify-center p-4">
    <div class="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div class="p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-2xl font-secondary font-semibold">Service Details</h3>
                <button onclick="closeServiceModal()" class="text-text-light hover:text-text-dark">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <div id="serviceModalContent">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<?php
// Additional JavaScript for services page
$inlineJS = '
function showServiceDetails(serviceId) {
    // This would typically fetch service details via AJAX
    document.getElementById("serviceModal").classList.remove("hidden");
    document.getElementById("serviceModal").classList.add("flex");
}

function closeServiceModal() {
    document.getElementById("serviceModal").classList.add("hidden");
    document.getElementById("serviceModal").classList.remove("flex");
}

// Close modal when clicking outside
document.getElementById("serviceModal").addEventListener("click", function(e) {
    if (e.target === this) {
        closeServiceModal();
    }
});
';

include 'includes/components/footer.php';
?>
