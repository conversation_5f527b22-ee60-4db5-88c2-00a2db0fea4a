<?php
/**
 * Landing Page
 * Redolence Salon & Spa
 */

require_once 'includes/database.php';
require_once 'includes/auth.php';

// Page meta data
$pageTitle = 'Home';
$pageDescription = 'Redolence Salon & Spa - Your Beauty, Our Passion. Experience luxury beauty and wellness services in Dar es Salaam. Professional hair styling, facial treatments, massage therapy, and more.';
$bodyClass = 'home-page';

// Additional CSS for home page
$additionalCSS = [
    ASSETS_URL . '/css/home.css'
];

// Get featured services (you can implement this later with database)
$featuredServices = [
    [
        'id' => 1,
        'name' => 'Signature Facial',
        'description' => 'Rejuvenate your skin with our signature facial treatment',
        'image' => ASSETS_URL . '/images/services/facial-treatment.jpg',
        'duration' => '60 minutes',
        'category' => 'facial'
    ],
    [
        'id' => 2,
        'name' => 'Relaxing Massage',
        'description' => 'Unwind with our therapeutic full-body massage',
        'image' => ASSETS_URL . '/images/services/massage-therapy.jpg',
        'duration' => '90 minutes',
        'category' => 'massage'
    ],
    [
        'id' => 3,
        'name' => 'Hair Styling',
        'description' => 'Transform your look with our expert hair styling',
        'image' => ASSETS_URL . '/images/services/hair-styling.jpg',
        'duration' => '120 minutes',
        'category' => 'hair'
    ]
];

include 'includes/components/header.php';
?>

<!-- Hero Section -->
<section class="hero-section">
    <div class="hero-slider">
        <div class="hero-slide active" style="background-image: url('https://images.unsplash.com/photo-1560750588-73207b1ef5b8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2069&q=80');">
            <div class="hero-overlay"></div>
            <div class="container">
                <div class="hero-content">
                    <h1 class="hero-title">Discover Your Natural Beauty</h1>
                    <p class="hero-subtitle">Experience luxury and relaxation at our premier salon and spa</p>
                    <div class="hero-buttons">
                        <a href="<?php echo BASE_URL; ?>/services" class="btn btn-primary btn-lg">Explore Services</a>
                        <a href="<?php echo BASE_URL; ?>/contact" class="btn btn-outline btn-lg">Book Appointment</a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="hero-slide" style="background-image: url('https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80');">
            <div class="hero-overlay"></div>
            <div class="container">
                <div class="hero-content">
                    <h1 class="hero-title">Professional Beauty Services</h1>
                    <p class="hero-subtitle">From hair styling to spa treatments, we've got you covered</p>
                    <div class="hero-buttons">
                        <a href="<?php echo BASE_URL; ?>/packages" class="btn btn-primary btn-lg">View Packages</a>
                        <a href="<?php echo BASE_URL; ?>/gallery" class="btn btn-outline btn-lg">View Gallery</a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="hero-slide" style="background-image: url('https://images.unsplash.com/photo-1544161515-4ab6ce6db874?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80');">
            <div class="hero-overlay"></div>
            <div class="container">
                <div class="hero-content">
                    <h1 class="hero-title">Relax & Rejuvenate</h1>
                    <p class="hero-subtitle">Escape the stress of daily life in our tranquil spa environment</p>
                    <div class="hero-buttons">
                        <a href="<?php echo BASE_URL; ?>/about" class="btn btn-primary btn-lg">About Us</a>
                        <a href="<?php echo BASE_URL; ?>/contact" class="btn btn-outline btn-lg">Contact Us</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Hero Navigation -->
    <div class="hero-nav">
        <button class="hero-prev"><i class="fas fa-chevron-left"></i></button>
        <button class="hero-next"><i class="fas fa-chevron-right"></i></button>
    </div>
    
    <!-- Hero Indicators -->
    <div class="hero-indicators">
        <button class="indicator active" data-slide="0"></button>
        <button class="indicator" data-slide="1"></button>
        <button class="indicator" data-slide="2"></button>
    </div>
</section>

<!-- Welcome Section -->
<section class="welcome-section">
    <div class="container">
        <div class="section-header text-center">
            <h2>Welcome to <?php echo APP_NAME; ?></h2>
            <p class="section-subtitle">Where beauty meets wellness</p>
        </div>
        
        <div class="welcome-content">
            <div class="welcome-text">
                <h3>Your Beauty, Our Passion</h3>
                <p>At Redolence Salon & Spa, we believe that true beauty comes from feeling confident and comfortable in your own skin. Our team of experienced professionals is dedicated to providing you with exceptional beauty and wellness services in a luxurious and relaxing environment.</p>
                
                <div class="welcome-features">
                    <div class="feature">
                        <i class="fas fa-award"></i>
                        <div>
                            <h4>Expert Professionals</h4>
                            <p>Certified and experienced beauty specialists</p>
                        </div>
                    </div>
                    <div class="feature">
                        <i class="fas fa-leaf"></i>
                        <div>
                            <h4>Natural Products</h4>
                            <p>Premium organic and natural beauty products</p>
                        </div>
                    </div>
                    <div class="feature">
                        <i class="fas fa-spa"></i>
                        <div>
                            <h4>Relaxing Environment</h4>
                            <p>Tranquil and luxurious spa atmosphere</p>
                        </div>
                    </div>
                </div>
                
                <a href="<?php echo BASE_URL; ?>/about" class="btn btn-primary">Learn More About Us</a>
            </div>
            
            <div class="welcome-image">
                <img src="https://images.unsplash.com/photo-1487412947147-5cebf100ffc2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2071&q=80" alt="Spa Environment" class="img-fluid">
            </div>
        </div>
    </div>
</section>

<!-- Featured Services Section -->
<section class="featured-services-section">
    <div class="container">
        <div class="section-header text-center">
            <h2>Our Featured Services</h2>
            <p class="section-subtitle">Discover our most popular treatments</p>
        </div>
        
        <div class="services-grid">
            <?php foreach ($featuredServices as $service): ?>
            <div class="service-card">
                <div class="service-image">
                    <img src="<?php echo $service['image']; ?>" alt="<?php echo htmlspecialchars($service['name']); ?>">
                    <div class="service-overlay">
                        <a href="<?php echo BASE_URL; ?>/services?category=<?php echo $service['category']; ?>" class="btn btn-primary">View Details</a>
                    </div>
                </div>
                <div class="service-content">
                    <h3><?php echo htmlspecialchars($service['name']); ?></h3>
                    <p><?php echo htmlspecialchars($service['description']); ?></p>
                    <div class="service-meta">
                        <span class="duration"><i class="fas fa-clock"></i> <?php echo $service['duration']; ?></span>
                        <a href="<?php echo BASE_URL; ?>/contact?service=<?php echo $service['id']; ?>" class="book-now">Book Now</a>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        
        <div class="text-center">
            <a href="<?php echo BASE_URL; ?>/services" class="btn btn-outline btn-lg">View All Services</a>
        </div>
    </div>
</section>

<!-- Categories Section -->
<section class="categories-section">
    <div class="container">
        <div class="section-header text-center">
            <h2>Service Categories</h2>
            <p class="section-subtitle">Choose from our wide range of beauty and wellness services</p>
        </div>
        
        <div class="categories-grid">
            <div class="category-card">
                <div class="category-image">
                    <img src="https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2069&q=80" alt="Hair Services">
                </div>
                <div class="category-content">
                    <h3>Hair Services</h3>
                    <p>Professional hair cutting, styling, coloring, and treatments</p>
                    <a href="<?php echo BASE_URL; ?>/services?category=hair" class="btn btn-primary">Explore</a>
                </div>
            </div>
            
            <div class="category-card">
                <div class="category-image">
                    <img src="https://images.unsplash.com/photo-1616394584738-fc6e612e71b9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80" alt="Facial Treatments">
                </div>
                <div class="category-content">
                    <h3>Facial Treatments</h3>
                    <p>Rejuvenating facials and skincare treatments for all skin types</p>
                    <a href="<?php echo BASE_URL; ?>/services?category=facial" class="btn btn-primary">Explore</a>
                </div>
            </div>
            
            <div class="category-card">
                <div class="category-image">
                    <img src="https://images.unsplash.com/photo-1544161515-4ab6ce6db874?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80" alt="Massage Therapy">
                </div>
                <div class="category-content">
                    <h3>Massage Therapy</h3>
                    <p>Relaxing and therapeutic massage treatments</p>
                    <a href="<?php echo BASE_URL; ?>/services?category=massage" class="btn btn-primary">Explore</a>
                </div>
            </div>
            
            <div class="category-card">
                <div class="category-image">
                    <img src="https://images.unsplash.com/photo-1604654894610-df63bc536371?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1887&q=80" alt="Nail Care">
                </div>
                <div class="category-content">
                    <h3>Nail Care</h3>
                    <p>Manicures, pedicures, and nail art services</p>
                    <a href="<?php echo BASE_URL; ?>/services?category=nails" class="btn btn-primary">Explore</a>
                </div>
            </div>
        </div>
    </div>
</section>

<?php
// Additional JavaScript for home page
$inlineJS = '
// Hero Slider
let currentSlide = 0;
const slides = document.querySelectorAll(".hero-slide");
const indicators = document.querySelectorAll(".indicator");

function showSlide(index) {
    slides.forEach(slide => slide.classList.remove("active"));
    indicators.forEach(indicator => indicator.classList.remove("active"));
    
    slides[index].classList.add("active");
    indicators[index].classList.add("active");
    currentSlide = index;
}

function nextSlide() {
    currentSlide = (currentSlide + 1) % slides.length;
    showSlide(currentSlide);
}

function prevSlide() {
    currentSlide = (currentSlide - 1 + slides.length) % slides.length;
    showSlide(currentSlide);
}

// Auto-play slider
setInterval(nextSlide, 5000);

// Navigation buttons
document.querySelector(".hero-next").addEventListener("click", nextSlide);
document.querySelector(".hero-prev").addEventListener("click", prevSlide);

// Indicators
indicators.forEach((indicator, index) => {
    indicator.addEventListener("click", () => showSlide(index));
});
';

include 'includes/components/footer.php';
?>
