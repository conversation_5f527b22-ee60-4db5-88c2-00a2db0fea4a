<?php
/**
 * Landing Page
 * Redolence Salon & Spa
 */

// Load configuration first
require_once 'config/app.php';
require_once 'includes/database.php';
require_once 'includes/auth.php';

// Page meta data
$pageTitle = 'Home';
$pageDescription = 'Redolence Salon & Spa - Your Beauty, Our Passion. Experience luxury beauty and wellness services in Dar es Salaam. Professional hair styling, facial treatments, massage therapy, and more.';
$bodyClass = 'home-page';

// Additional CSS for home page
$additionalCSS = [
    ASSETS_URL . '/css/home.css'
];

// Get featured services (you can implement this later with database)
$featuredServices = [
    [
        'id' => 1,
        'name' => 'Signature Facial',
        'description' => 'Rejuvenate your skin with our signature facial treatment',
        'image' => ASSETS_URL . '/images/services/facial-treatment.jpg',
        'duration' => '60 minutes',
        'category' => 'facial'
    ],
    [
        'id' => 2,
        'name' => 'Relaxing Massage',
        'description' => 'Unwind with our therapeutic full-body massage',
        'image' => ASSETS_URL . '/images/services/massage-therapy.jpg',
        'duration' => '90 minutes',
        'category' => 'massage'
    ],
    [
        'id' => 3,
        'name' => 'Hair Styling',
        'description' => 'Transform your look with our expert hair styling',
        'image' => ASSETS_URL . '/images/services/hair-styling.jpg',
        'duration' => '120 minutes',
        'category' => 'hair'
    ]
];

include 'includes/components/header.php';
?>

<!-- Luxury Hero Section -->
<section class="hero-section">
    <div class="hero-slider">
        <div class="hero-slide active" style="background-image: url('https://images.unsplash.com/photo-1560750588-73207b1ef5b8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2069&q=80');">
            <div class="hero-overlay"></div>
            <div class="container">
                <div class="hero-content">
                    <h1 class="hero-title">Discover Your Natural Beauty</h1>
                    <p class="hero-subtitle">Experience luxury and relaxation at our premier salon and spa where every detail is crafted for your ultimate comfort and transformation</p>
                    <div class="hero-buttons">
                        <a href="<?php echo BASE_URL; ?>/services" class="btn btn-primary btn-lg">
                            <i class="fas fa-sparkles mr-3"></i>
                            Explore Services
                        </a>
                        <a href="<?php echo BASE_URL; ?>/contact" class="btn btn-ghost btn-lg">
                            <i class="fas fa-calendar-alt mr-3"></i>
                            Book Appointment
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="hero-slide" style="background-image: url('https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80');">
            <div class="hero-overlay"></div>
            <div class="container">
                <div class="hero-content">
                    <h1 class="hero-title">Professional Beauty Services</h1>
                    <p class="hero-subtitle">From expert hair styling to rejuvenating spa treatments, our skilled professionals deliver exceptional results tailored to your unique needs</p>
                    <div class="hero-buttons">
                        <a href="<?php echo BASE_URL; ?>/packages" class="btn btn-primary btn-lg">
                            <i class="fas fa-gift mr-3"></i>
                            View Packages
                        </a>
                        <a href="<?php echo BASE_URL; ?>/gallery" class="btn btn-ghost btn-lg">
                            <i class="fas fa-images mr-3"></i>
                            View Gallery
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="hero-slide" style="background-image: url('https://images.unsplash.com/photo-1544161515-4ab6ce6db874?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80');">
            <div class="hero-overlay"></div>
            <div class="container">
                <div class="hero-content">
                    <h1 class="hero-title">Relax & Rejuvenate</h1>
                    <p class="hero-subtitle">Escape the stress of daily life in our tranquil spa environment designed to restore your mind, body, and spirit through luxurious treatments</p>
                    <div class="hero-buttons">
                        <a href="<?php echo BASE_URL; ?>/about" class="btn btn-primary btn-lg">
                            <i class="fas fa-heart mr-3"></i>
                            About Us
                        </a>
                        <a href="<?php echo BASE_URL; ?>/contact" class="btn btn-ghost btn-lg">
                            <i class="fas fa-phone mr-3"></i>
                            Contact Us
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Luxury Hero Navigation -->
    <div class="hero-nav">
        <button class="hero-prev"><i class="fas fa-chevron-left"></i></button>
        <button class="hero-next"><i class="fas fa-chevron-right"></i></button>
    </div>

    <!-- Luxury Hero Indicators -->
    <div class="hero-indicators">
        <button class="indicator active" data-slide="0"></button>
        <button class="indicator" data-slide="1"></button>
        <button class="indicator" data-slide="2"></button>
    </div>

    <!-- Hero Scroll Indicator -->
    <div class="hero-scroll">
        <div class="flex flex-col items-center">
            <span class="text-xs mb-2">SCROLL TO EXPLORE</span>
            <i class="fas fa-chevron-down"></i>
        </div>
    </div>
</section>

<!-- Luxury Welcome Section -->
<section class="welcome-section">
    <div class="container">
        <div class="text-center mb-16 animate-on-scroll">
            <span class="inline-block px-6 py-2 bg-primary-green/10 text-primary-green rounded-full text-sm font-semibold tracking-wider uppercase mb-6">Welcome to Excellence</span>
            <h2 class="text-5xl lg:text-6xl font-secondary font-bold text-gray-900 mb-8 leading-tight">Welcome to <?php echo APP_NAME; ?></h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">Where beauty meets wellness in an atmosphere of luxury and tranquility</p>
        </div>

        <div class="welcome-content">
            <div class="welcome-text animate-on-scroll">
                <h3>Your Beauty, Our Passion</h3>
                <p class="text-xl leading-relaxed mb-8">At Redolence Salon & Spa, we believe that true beauty comes from feeling confident and comfortable in your own skin. Our team of experienced professionals is dedicated to providing you with exceptional beauty and wellness services in a luxurious and relaxing environment.</p>

                <div class="welcome-features">
                    <div class="feature">
                        <i class="fas fa-award"></i>
                        <div class="feature-content">
                            <h4>Expert Professionals</h4>
                            <p>Certified and experienced beauty specialists with years of training in the latest techniques and trends</p>
                        </div>
                    </div>
                    <div class="feature">
                        <i class="fas fa-leaf"></i>
                        <div class="feature-content">
                            <h4>Natural Products</h4>
                            <p>Premium organic and natural beauty products sourced from the finest ingredients worldwide</p>
                        </div>
                    </div>
                    <div class="feature">
                        <i class="fas fa-spa"></i>
                        <div class="feature-content">
                            <h4>Relaxing Environment</h4>
                            <p>Tranquil and luxurious spa atmosphere designed to transport you to a world of serenity</p>
                        </div>
                    </div>
                </div>

                <div class="flex gap-4 mt-8">
                    <a href="<?php echo BASE_URL; ?>/about" class="btn btn-primary">
                        <i class="fas fa-arrow-right mr-2"></i>
                        Learn More About Us
                    </a>
                    <a href="<?php echo BASE_URL; ?>/contact" class="btn btn-outline">
                        <i class="fas fa-phone mr-2"></i>
                        Get In Touch
                    </a>
                </div>
            </div>

            <div class="welcome-image animate-on-scroll">
                <img src="https://images.unsplash.com/photo-1487412947147-5cebf100ffc2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2071&q=80" alt="Luxury Spa Environment" class="img-fluid">
            </div>
        </div>
    </div>
</section>

<!-- Luxury Stats Section -->
<section class="stats-section">
    <div class="container">
        <div class="stats-grid">
            <div class="stat-item animate-on-scroll">
                <div class="stat-number">500+</div>
                <div class="stat-label">Happy Clients</div>
            </div>
            <div class="stat-item animate-on-scroll">
                <div class="stat-number">15+</div>
                <div class="stat-label">Expert Staff</div>
            </div>
            <div class="stat-item animate-on-scroll">
                <div class="stat-number">50+</div>
                <div class="stat-label">Premium Services</div>
            </div>
            <div class="stat-item animate-on-scroll">
                <div class="stat-number">5</div>
                <div class="stat-label">Years Experience</div>
            </div>
        </div>
    </div>
</section>

<!-- Luxury Featured Services Section -->
<section class="featured-services-section">
    <div class="container">
        <div class="text-center mb-16 animate-on-scroll">
            <span class="inline-block px-6 py-2 bg-primary-green/10 text-primary-green rounded-full text-sm font-semibold tracking-wider uppercase mb-6">Premium Treatments</span>
            <h2 class="text-5xl lg:text-6xl font-secondary font-bold text-gray-900 mb-8 leading-tight">Our Featured Services</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">Discover our most popular treatments designed to enhance your natural beauty and provide ultimate relaxation</p>
        </div>

        <div class="services-grid">
            <?php foreach ($featuredServices as $service): ?>
            <div class="service-card animate-on-scroll">
                <div class="service-image">
                    <img src="<?php echo $service['image']; ?>" alt="<?php echo htmlspecialchars($service['name']); ?>">
                    <div class="service-overlay">
                        <a href="<?php echo BASE_URL; ?>/services?category=<?php echo $service['category']; ?>" class="btn btn-ghost">
                            <i class="fas fa-eye mr-2"></i>
                            View Details
                        </a>
                    </div>
                </div>
                <div class="service-content">
                    <h3><?php echo htmlspecialchars($service['name']); ?></h3>
                    <p><?php echo htmlspecialchars($service['description']); ?></p>
                    <div class="service-meta">
                        <span class="duration"><i class="fas fa-clock"></i> <?php echo $service['duration']; ?> min</span>
                        <a href="<?php echo BASE_URL; ?>/contact?service=<?php echo $service['id']; ?>" class="book-now">Book Now</a>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>

        <div class="text-center mt-16 animate-on-scroll">
            <a href="<?php echo BASE_URL; ?>/services" class="btn btn-outline btn-lg">
                <i class="fas fa-th-large mr-3"></i>
                View All Services
            </a>
        </div>
    </div>
</section>

<!-- Luxury Categories Section -->
<section class="categories-section">
    <div class="container">
        <div class="text-center mb-16 animate-on-scroll">
            <span class="inline-block px-6 py-2 bg-white/20 text-white rounded-full text-sm font-semibold tracking-wider uppercase mb-6">Service Categories</span>
            <h2 class="text-5xl lg:text-6xl font-secondary font-bold text-white mb-8 leading-tight">Choose Your Experience</h2>
            <p class="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">Explore our comprehensive range of beauty and wellness services, each designed to provide you with exceptional results</p>
        </div>

        <div class="categories-grid">
            <div class="category-card animate-on-scroll">
                <div class="category-image">
                    <img src="https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2069&q=80" alt="Hair Services">
                </div>
                <div class="category-content">
                    <h3>Hair Services</h3>
                    <p>Professional hair cutting, styling, coloring, and treatments using premium products and latest techniques</p>
                    <a href="<?php echo BASE_URL; ?>/services?category=hair" class="btn btn-ghost">
                        <i class="fas fa-cut mr-2"></i>
                        Explore Hair Services
                    </a>
                </div>
            </div>

            <div class="category-card animate-on-scroll">
                <div class="category-image">
                    <img src="https://images.unsplash.com/photo-1616394584738-fc6e612e71b9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80" alt="Facial Treatments">
                </div>
                <div class="category-content">
                    <h3>Facial Treatments</h3>
                    <p>Rejuvenating facials and advanced skincare treatments tailored for all skin types and concerns</p>
                    <a href="<?php echo BASE_URL; ?>/services?category=facial" class="btn btn-ghost">
                        <i class="fas fa-leaf mr-2"></i>
                        Explore Facials
                    </a>
                </div>
            </div>

            <div class="category-card animate-on-scroll">
                <div class="category-image">
                    <img src="https://images.unsplash.com/photo-1544161515-4ab6ce6db874?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80" alt="Massage Therapy">
                </div>
                <div class="category-content">
                    <h3>Massage Therapy</h3>
                    <p>Relaxing and therapeutic massage treatments to restore balance and relieve stress</p>
                    <a href="<?php echo BASE_URL; ?>/services?category=massage" class="btn btn-ghost">
                        <i class="fas fa-spa mr-2"></i>
                        Explore Massages
                    </a>
                </div>
            </div>

            <div class="category-card animate-on-scroll">
                <div class="category-image">
                    <img src="https://images.unsplash.com/photo-1604654894610-df63bc536371?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1887&q=80" alt="Nail Care">
                </div>
                <div class="category-content">
                    <h3>Nail Care</h3>
                    <p>Professional manicures, pedicures, and artistic nail designs for beautiful, healthy nails</p>
                    <a href="<?php echo BASE_URL; ?>/services?category=nails" class="btn btn-ghost">
                        <i class="fas fa-hand-sparkles mr-2"></i>
                        Explore Nail Care
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<?php
// Enhanced JavaScript for luxury home page
$inlineJS = '
// Luxury Hero Slider with enhanced animations
let currentSlide = 0;
const slides = document.querySelectorAll(".hero-slide");
const indicators = document.querySelectorAll(".indicator");
let autoPlayInterval;

function showSlide(index) {
    slides.forEach((slide, i) => {
        slide.classList.remove("active");
        if (i === index) {
            slide.classList.add("active");
        }
    });

    indicators.forEach((indicator, i) => {
        indicator.classList.remove("active");
        if (i === index) {
            indicator.classList.add("active");
        }
    });

    currentSlide = index;
}

function nextSlide() {
    currentSlide = (currentSlide + 1) % slides.length;
    showSlide(currentSlide);
}

function prevSlide() {
    currentSlide = (currentSlide - 1 + slides.length) % slides.length;
    showSlide(currentSlide);
}

function startAutoPlay() {
    autoPlayInterval = setInterval(nextSlide, 7000);
}

function stopAutoPlay() {
    clearInterval(autoPlayInterval);
}

// Initialize slider
startAutoPlay();

// Navigation buttons with enhanced interaction
document.querySelector(".hero-next").addEventListener("click", () => {
    nextSlide();
    stopAutoPlay();
    setTimeout(startAutoPlay, 10000);
});

document.querySelector(".hero-prev").addEventListener("click", () => {
    prevSlide();
    stopAutoPlay();
    setTimeout(startAutoPlay, 10000);
});

// Indicators with enhanced interaction
indicators.forEach((indicator, index) => {
    indicator.addEventListener("click", () => {
        showSlide(index);
        stopAutoPlay();
        setTimeout(startAutoPlay, 10000);
    });
});

// Pause on hover
document.querySelector(".hero-section").addEventListener("mouseenter", stopAutoPlay);
document.querySelector(".hero-section").addEventListener("mouseleave", startAutoPlay);

// Scroll animations
const observerOptions = {
    threshold: 0.1,
    rootMargin: "0px 0px -50px 0px"
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.classList.add("animated");
        }
    });
}, observerOptions);

// Observe all elements with animate-on-scroll class
document.querySelectorAll(".animate-on-scroll").forEach(el => {
    observer.observe(el);
});

// Smooth scroll for hero scroll indicator
document.querySelector(".hero-scroll").addEventListener("click", () => {
    document.querySelector(".welcome-section").scrollIntoView({
        behavior: "smooth"
    });
});

// Enhanced service card interactions
document.querySelectorAll(".service-card").forEach(card => {
    card.addEventListener("mouseenter", () => {
        card.style.transform = "translateY(-8px) scale(1.02)";
    });

    card.addEventListener("mouseleave", () => {
        card.style.transform = "translateY(0) scale(1)";
    });
});

// Parallax effect for hero background
window.addEventListener("scroll", () => {
    const scrolled = window.pageYOffset;
    const heroSection = document.querySelector(".hero-section");
    if (heroSection) {
        const rate = scrolled * -0.5;
        heroSection.style.transform = `translateY(${rate}px)`;
    }
});

// Counter animation for stats
function animateCounter(element, target) {
    let current = 0;
    const increment = target / 100;
    const timer = setInterval(() => {
        current += increment;
        if (current >= target) {
            current = target;
            clearInterval(timer);
        }
        element.textContent = Math.floor(current) + (element.textContent.includes("+") ? "+" : "");
    }, 20);
}

// Animate stats when they come into view
const statsObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            const number = entry.target.querySelector(".stat-number");
            const target = parseInt(number.textContent);
            animateCounter(number, target);
            statsObserver.unobserve(entry.target);
        }
    });
});

document.querySelectorAll(".stat-item").forEach(item => {
    statsObserver.observe(item);
});
';

include 'includes/components/footer.php';
?>
