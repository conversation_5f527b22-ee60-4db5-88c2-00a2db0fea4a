<?php
// Simple PHP test file
echo "PHP is working!<br>";
echo "PHP Version: " . phpversion() . "<br>";
echo "Current directory: " . __DIR__ . "<br>";

// Test if files exist
$files_to_check = [
    'config/app.php',
    'includes/database.php',
    'includes/auth.php',
    'includes/components/header.php',
    'includes/components/footer.php'
];

echo "<h3>File Check:</h3>";
foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        echo "✓ $file exists<br>";
    } else {
        echo "✗ $file missing<br>";
    }
}

// Test configuration loading
try {
    require_once 'config/app.php';
    echo "<h3>Configuration loaded successfully!</h3>";
    echo "APP_NAME: " . APP_NAME . "<br>";
    echo "BASE_URL: " . BASE_URL . "<br>";
} catch (Exception $e) {
    echo "<h3>Configuration error:</h3>";
    echo $e->getMessage();
}
?>
