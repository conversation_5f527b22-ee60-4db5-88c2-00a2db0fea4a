# Simple .htaccess for testing
# Comment out complex rules that might cause issues

# Basic PHP error display for debugging
# php_flag display_errors on
# php_value error_reporting "E_ALL"

# Basic rewrite rules (commented out for now)
# RewriteEngine On

# Hide .php extensions (commented out for testing)
# RewriteCond %{REQUEST_FILENAME} !-d
# RewriteCond %{REQUEST_FILENAME} !-f
# RewriteRule ^([^\.]+)$ $1.php [NC,L]
