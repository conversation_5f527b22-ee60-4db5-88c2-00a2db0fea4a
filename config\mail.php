<?php
/**
 * Mail Configuration
 * Redolence Salon & Spa
 */

// SMTP Configuration
define('MAIL_HOST', 'smtp.gmail.com');
define('MAIL_PORT', 587);
define('MAIL_USERNAME', '<EMAIL>');
define('MAIL_PASSWORD', 'your-app-password');
define('MAIL_ENCRYPTION', 'tls'); // tls or ssl
define('MAIL_FROM_ADDRESS', SMTP_FROM_EMAIL);
define('MAIL_FROM_NAME', SMTP_FROM_NAME);

// Email templates directory
define('EMAIL_TEMPLATES_PATH', ROOT_PATH . '/includes/email-templates');

// Email settings
define('MAIL_QUEUE_ENABLED', false); // Set to true for production
define('MAIL_LOG_ENABLED', true);
define('MAIL_DEBUG', APP_DEBUG);

/**
 * Send email using PHPMailer or basic mail function
 */
function sendEmail($to, $subject, $body, $isHTML = true, $attachments = []) {
    // For now, we'll use <PERSON><PERSON>'s basic mail function
    // In production, consider using PHPMailer for better reliability
    
    $headers = [];
    $headers[] = 'From: ' . MAIL_FROM_NAME . ' <' . MAIL_FROM_ADDRESS . '>';
    $headers[] = 'Reply-To: ' . MAIL_FROM_ADDRESS;
    $headers[] = 'X-Mailer: PHP/' . phpversion();
    
    if ($isHTML) {
        $headers[] = 'Content-Type: text/html; charset=UTF-8';
    } else {
        $headers[] = 'Content-Type: text/plain; charset=UTF-8';
    }
    
    $headerString = implode("\r\n", $headers);
    
    try {
        $result = mail($to, $subject, $body, $headerString);
        
        if (MAIL_LOG_ENABLED) {
            $logEntry = date('Y-m-d H:i:s') . " - Email sent to: {$to}, Subject: {$subject}, Result: " . ($result ? 'Success' : 'Failed') . "\n";
            file_put_contents(ROOT_PATH . '/logs/mail.log', $logEntry, FILE_APPEND | LOCK_EX);
        }
        
        return $result;
    } catch (Exception $e) {
        if (MAIL_DEBUG) {
            error_log("Email sending failed: " . $e->getMessage());
        }
        return false;
    }
}

/**
 * Load email template
 */
function loadEmailTemplate($templateName, $variables = []) {
    $templatePath = EMAIL_TEMPLATES_PATH . '/' . $templateName . '.php';
    
    if (!file_exists($templatePath)) {
        throw new Exception("Email template not found: {$templateName}");
    }
    
    // Extract variables for use in template
    extract($variables);
    
    ob_start();
    include $templatePath;
    return ob_get_clean();
}

/**
 * Send booking confirmation email
 */
function sendBookingConfirmation($customerEmail, $bookingDetails) {
    $subject = 'Booking Inquiry Received - ' . APP_NAME;
    $body = loadEmailTemplate('booking-confirmation', [
        'customerName' => $bookingDetails['customer_name'],
        'serviceName' => $bookingDetails['service_name'],
        'preferredDate' => $bookingDetails['preferred_date'],
        'preferredTime' => $bookingDetails['preferred_time'],
        'message' => $bookingDetails['message'] ?? ''
    ]);
    
    return sendEmail($customerEmail, $subject, $body, true);
}

/**
 * Send contact form notification
 */
function sendContactNotification($contactDetails) {
    $subject = 'New Contact Form Submission - ' . APP_NAME;
    $body = loadEmailTemplate('contact-notification', $contactDetails);
    
    return sendEmail(BUSINESS_EMAIL, $subject, $body, true);
}

/**
 * Send newsletter subscription confirmation
 */
function sendNewsletterConfirmation($email, $name = '') {
    $subject = 'Welcome to ' . APP_NAME . ' Newsletter';
    $body = loadEmailTemplate('newsletter-welcome', [
        'name' => $name,
        'email' => $email
    ]);
    
    return sendEmail($email, $subject, $body, true);
}
