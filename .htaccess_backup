RewriteEngine On

# Security Headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"

# Hide .php extensions
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteRule ^([^\.]+)$ $1.php [NC,L]

# Redirect .php to clean URLs
RewriteCond %{THE_REQUEST} /([^.]+)\.php [NC]
RewriteRule ^ /%1 [NC,L,R=301]

# Admin panel routing
RewriteRule ^admin/([^/]+)/([^/]+)/?$ admin/$1/$2.php [NC,L]
RewriteRule ^admin/([^/]+)/?$ admin/$1/index.php [NC,L]

# Customer panel routing
RewriteRule ^customer/([^/]+)/([^/]+)/?$ customer/$1/$2.php [NC,L]
RewriteRule ^customer/([^/]+)/?$ customer/$1/index.php [NC,L]

# Staff panel routing
RewriteRule ^staff/([^/]+)/([^/]+)/?$ staff/$1/$2.php [NC,L]
RewriteRule ^staff/([^/]+)/?$ staff/$1/index.php [NC,L]

# API routing
RewriteRule ^api/([^/]+)/([^/]+)/?$ api/$1/$2.php [NC,L]
RewriteRule ^api/([^/]+)/?$ api/$1/index.php [NC,L]

# Protect sensitive directories
RewriteRule ^(config|includes|database|cron|uploads/backups)/ - [F,L]

# Cache static assets
<FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$">
    ExpiresActive On
    ExpiresDefault "access plus 1 month"
</FilesMatch>

# Compress text files
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Error pages
ErrorDocument 404 /404.php
ErrorDocument 403 /403.php
ErrorDocument 500 /500.php
