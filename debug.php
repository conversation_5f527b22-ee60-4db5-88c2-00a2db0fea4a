<?php
// Debug file to identify the error
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "Starting debug...<br>";

try {
    echo "1. Loading config/app.php...<br>";
    require_once 'config/app.php';
    echo "✓ Config loaded successfully<br>";
    
    echo "2. Loading includes/database.php...<br>";
    require_once 'includes/database.php';
    echo "✓ Database utilities loaded<br>";
    
    echo "3. Loading includes/auth.php...<br>";
    require_once 'includes/auth.php';
    echo "✓ Auth functions loaded<br>";
    
    echo "4. Testing constants...<br>";
    echo "APP_NAME: " . APP_NAME . "<br>";
    echo "BASE_URL: " . BASE_URL . "<br>";
    echo "ASSETS_URL: " . ASSETS_URL . "<br>";
    
    echo "5. Loading header component...<br>";
    $pageTitle = 'Debug Page';
    $pageDescription = 'Debug page';
    $bodyClass = 'debug-page';
    
    include 'includes/components/header.php';
    
    echo "<h1>Debug successful!</h1>";
    echo "<p>If you see this, the basic setup is working.</p>";
    
    include 'includes/components/footer.php';
    
} catch (Exception $e) {
    echo "<h2>Error occurred:</h2>";
    echo "<p><strong>Message:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "<p><strong>Stack trace:</strong></p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
} catch (Error $e) {
    echo "<h2>Fatal Error occurred:</h2>";
    echo "<p><strong>Message:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "<p><strong>Stack trace:</strong></p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
