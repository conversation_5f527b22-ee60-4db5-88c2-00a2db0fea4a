<?php
/**
 * Contact Page
 * Redolence Salon & Spa
 */

// Load configuration first
require_once 'config/app.php';
require_once 'includes/database.php';
require_once 'includes/auth.php';

// Page meta data
$pageTitle = 'Contact Us';
$pageDescription = 'Get in touch with Redolence Salon & Spa. Book your appointment, ask questions, or visit our location in Dar es Salaam. We\'re here to help you with all your beauty and wellness needs.';
$bodyClass = 'contact-page';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['submit_contact'])) {
    $name = sanitizeInput($_POST['name'] ?? '');
    $email = sanitizeInput($_POST['email'] ?? '');
    $phone = sanitizeInput($_POST['phone'] ?? '');
    $subject = sanitizeInput($_POST['subject'] ?? '');
    $message = sanitizeInput($_POST['message'] ?? '');
    
    $errors = [];
    
    // Validation
    if (empty($name)) $errors[] = 'Name is required';
    if (empty($email)) $errors[] = 'Email is required';
    if (!isValidEmail($email)) $errors[] = 'Please enter a valid email address';
    if (empty($subject)) $errors[] = 'Subject is required';
    if (empty($message)) $errors[] = 'Message is required';
    
    if (empty($errors)) {
        // Here you would typically save to database and send email
        // For now, we'll just show a success message
        redirect('/contact', 'Thank you for your message! We\'ll get back to you soon.', 'success');
    }
}

include 'includes/components/header.php';
?>

<!-- Page Header -->
<section class="bg-gradient-to-r from-primary-green to-primary-blue text-white py-20">
    <div class="container">
        <div class="text-center">
            <h1 class="text-5xl font-secondary font-bold mb-4">Contact Us</h1>
            <p class="text-xl opacity-90 max-w-2xl mx-auto">Get in touch with us to book your appointment or ask any questions. We're here to help you achieve your beauty and wellness goals.</p>
        </div>
    </div>
</section>

<!-- Contact Section -->
<section class="py-20 bg-white">
    <div class="container">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <!-- Contact Form -->
            <div>
                <h2 class="text-3xl font-secondary font-semibold text-text-dark mb-6">Send us a Message</h2>
                
                <?php if (!empty($errors)): ?>
                    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                        <ul class="list-disc list-inside">
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo htmlspecialchars($error); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>
                
                <form method="POST" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="name" class="block text-sm font-medium text-text-dark mb-2">Full Name *</label>
                            <input type="text" id="name" name="name" required 
                                   class="w-full px-4 py-3 border-2 border-border rounded-lg focus:border-primary-green focus:outline-none transition-colors duration-300"
                                   value="<?php echo htmlspecialchars($_POST['name'] ?? ''); ?>">
                        </div>
                        <div>
                            <label for="email" class="block text-sm font-medium text-text-dark mb-2">Email Address *</label>
                            <input type="email" id="email" name="email" required 
                                   class="w-full px-4 py-3 border-2 border-border rounded-lg focus:border-primary-green focus:outline-none transition-colors duration-300"
                                   value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>">
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="phone" class="block text-sm font-medium text-text-dark mb-2">Phone Number</label>
                            <input type="tel" id="phone" name="phone" 
                                   class="w-full px-4 py-3 border-2 border-border rounded-lg focus:border-primary-green focus:outline-none transition-colors duration-300"
                                   value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>">
                        </div>
                        <div>
                            <label for="subject" class="block text-sm font-medium text-text-dark mb-2">Subject *</label>
                            <select id="subject" name="subject" required 
                                    class="w-full px-4 py-3 border-2 border-border rounded-lg focus:border-primary-green focus:outline-none transition-colors duration-300">
                                <option value="">Select a subject</option>
                                <option value="booking" <?php echo ($_POST['subject'] ?? '') === 'booking' ? 'selected' : ''; ?>>Book Appointment</option>
                                <option value="services" <?php echo ($_POST['subject'] ?? '') === 'services' ? 'selected' : ''; ?>>Service Inquiry</option>
                                <option value="packages" <?php echo ($_POST['subject'] ?? '') === 'packages' ? 'selected' : ''; ?>>Package Information</option>
                                <option value="general" <?php echo ($_POST['subject'] ?? '') === 'general' ? 'selected' : ''; ?>>General Question</option>
                                <option value="feedback" <?php echo ($_POST['subject'] ?? '') === 'feedback' ? 'selected' : ''; ?>>Feedback</option>
                            </select>
                        </div>
                    </div>
                    
                    <div>
                        <label for="message" class="block text-sm font-medium text-text-dark mb-2">Message *</label>
                        <textarea id="message" name="message" rows="6" required 
                                  class="w-full px-4 py-3 border-2 border-border rounded-lg focus:border-primary-green focus:outline-none transition-colors duration-300 resize-vertical"
                                  placeholder="Tell us about your needs, preferred dates, or any questions you have..."><?php echo htmlspecialchars($_POST['message'] ?? ''); ?></textarea>
                    </div>
                    
                    <button type="submit" name="submit_contact" class="btn btn-primary btn-lg w-full md:w-auto">
                        <i class="fas fa-paper-plane mr-2"></i>
                        Send Message
                    </button>
                </form>
            </div>
            
            <!-- Contact Information -->
            <div>
                <h2 class="text-3xl font-secondary font-semibold text-text-dark mb-6">Get in Touch</h2>
                
                <div class="space-y-6 mb-8">
                    <div class="flex items-start">
                        <div class="flex-shrink-0 w-12 h-12 bg-primary-green/10 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-map-marker-alt text-primary-green text-xl"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold text-text-dark mb-1">Visit Our Salon</h3>
                            <p class="text-text-light"><?php echo BUSINESS_ADDRESS; ?></p>
                        </div>
                    </div>
                    
                    <div class="flex items-start">
                        <div class="flex-shrink-0 w-12 h-12 bg-primary-green/10 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-phone text-primary-green text-xl"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold text-text-dark mb-1">Call Us</h3>
                            <p class="text-text-light">
                                <a href="tel:<?php echo str_replace(' ', '', BUSINESS_PHONE); ?>" class="hover:text-primary-green transition-colors duration-300">
                                    <?php echo BUSINESS_PHONE; ?>
                                </a>
                            </p>
                        </div>
                    </div>
                    
                    <div class="flex items-start">
                        <div class="flex-shrink-0 w-12 h-12 bg-primary-green/10 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-envelope text-primary-green text-xl"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold text-text-dark mb-1">Email Us</h3>
                            <p class="text-text-light">
                                <a href="mailto:<?php echo BUSINESS_EMAIL; ?>" class="hover:text-primary-green transition-colors duration-300">
                                    <?php echo BUSINESS_EMAIL; ?>
                                </a>
                            </p>
                        </div>
                    </div>
                    
                    <div class="flex items-start">
                        <div class="flex-shrink-0 w-12 h-12 bg-primary-green/10 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-clock text-primary-green text-xl"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold text-text-dark mb-1">Opening Hours</h3>
                            <p class="text-text-light"><?php echo BUSINESS_HOURS; ?></p>
                        </div>
                    </div>
                </div>
                
                <!-- Social Media -->
                <div>
                    <h3 class="font-semibold text-text-dark mb-4">Follow Us</h3>
                    <div class="flex space-x-4">
                        <a href="<?php echo SOCIAL_FACEBOOK; ?>" target="_blank" 
                           class="w-12 h-12 bg-primary-green/10 rounded-lg flex items-center justify-center text-primary-green hover:bg-primary-green hover:text-white transition-all duration-300">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="<?php echo SOCIAL_INSTAGRAM; ?>" target="_blank" 
                           class="w-12 h-12 bg-primary-green/10 rounded-lg flex items-center justify-center text-primary-green hover:bg-primary-green hover:text-white transition-all duration-300">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="<?php echo SOCIAL_TWITTER; ?>" target="_blank" 
                           class="w-12 h-12 bg-primary-green/10 rounded-lg flex items-center justify-center text-primary-green hover:bg-primary-green hover:text-white transition-all duration-300">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="<?php echo SOCIAL_YOUTUBE; ?>" target="_blank" 
                           class="w-12 h-12 bg-primary-green/10 rounded-lg flex items-center justify-center text-primary-green hover:bg-primary-green hover:text-white transition-all duration-300">
                            <i class="fab fa-youtube"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Map Section -->
<section class="py-20 bg-accent">
    <div class="container">
        <div class="text-center mb-12">
            <h2 class="text-4xl font-secondary font-semibold text-text-dark mb-4">Find Us</h2>
            <p class="text-lg text-text-light">Located in the heart of Dar es Salaam, easily accessible by car or public transport.</p>
        </div>
        
        <!-- Placeholder for Google Maps -->
        <div class="bg-gray-300 h-96 rounded-lg flex items-center justify-center">
            <div class="text-center">
                <i class="fas fa-map-marked-alt text-6xl text-gray-500 mb-4"></i>
                <p class="text-gray-600 text-lg">Interactive Map Coming Soon</p>
                <p class="text-gray-500">Google Maps integration will be added here</p>
            </div>
        </div>
    </div>
</section>

<?php include 'includes/components/footer.php'; ?>
