# Redolence Salon & Spa Website

A modern, responsive website for a luxury salon and spa built with PHP and Tailwind CSS.

## Features

- **Modern Design**: Clean, professional design with a focus on beauty and wellness
- **Responsive Layout**: Fully responsive design that works on all devices
- **Tailwind CSS**: Utility-first CSS framework for consistent styling
- **Service Management**: Comprehensive service catalog with categories
- **Contact System**: Contact forms and booking inquiries
- **User Authentication**: Customer, staff, and admin authentication
- **Database Integration**: MySQL database for data management

## Technology Stack

- **Backend**: PHP 8.0+
- **Database**: MySQL 8.0+
- **Frontend**: HTML5, Tailwind CSS, JavaScript
- **Icons**: Font Awesome 6
- **Fonts**: Google Fonts (Poppins, Playfair Display)

## Color Palette

The website uses a carefully selected color palette:

- **Primary Green**: `#49a75c` - Main brand color
- **Primary Blue**: `#5894d2` - Secondary brand color
- **Background**: `#ffffff` - Clean white background
- **Text Dark**: `#333333` - Primary text color
- **Text Light**: `#666666` - Secondary text color
- **Accent**: `#f8f9fa` - Light accent color
- **Border**: `#e9ecef` - Border color

## Project Structure

```
redolence-spa/
├── assets/
│   ├── css/
│   │   ├── main.css          # Main Tailwind CSS file
│   │   └── home.css          # Home page specific styles
│   ├── js/
│   │   └── main.js           # Main JavaScript file
│   └── images/
│       └── placeholder.txt   # Image requirements
├── config/
│   ├── app.php              # Application configuration
│   ├── database.php         # Database configuration
│   └── mail.php             # Email configuration
├── includes/
│   ├── components/
│   │   ├── header.php       # Header component
│   │   └── footer.php       # Footer component
│   ├── database.php         # Database utilities
│   └── auth.php             # Authentication functions
├── database/
│   └── schema.sql           # Database schema
├── index.php                # Landing page
├── services.php             # Services page
├── contact.php              # Contact page
├── tailwind.config.js       # Tailwind configuration
├── .htaccess               # URL rewriting rules
└── README.md               # This file
```

## Installation

1. **Clone or download** the project files to your web server directory

2. **Configure the database**:
   - Create a MySQL database named `redolence_spa`
   - Import the schema from `database/schema.sql`
   - Update database credentials in `config/database.php`

3. **Configure the application**:
   - Update settings in `config/app.php`
   - Set your domain URL in `APP_URL`
   - Configure email settings in `config/mail.php`

4. **Set up web server**:
   - Ensure PHP 8.0+ is installed
   - Enable mod_rewrite for Apache (for clean URLs)
   - Set appropriate file permissions

5. **Add images**:
   - Add your logo files to `assets/images/`
   - Add service and gallery images as specified in `assets/images/placeholder.txt`

## Tailwind CSS Integration

The website uses Tailwind CSS for styling with the following approach:

### CDN Integration
The project uses Tailwind CSS via CDN for quick setup and development:

```html
<script src="https://cdn.tailwindcss.com"></script>
```

### Custom Configuration
Tailwind is configured with custom colors and fonts in the header:

```javascript
tailwind.config = {
    theme: {
        extend: {
            colors: {
                'primary-green': '#49a75c',
                'primary-blue': '#5894d2',
                // ... other colors
            },
            fontFamily: {
                'primary': ['Poppins', 'sans-serif'],
                'secondary': ['Playfair Display', 'serif'],
            }
        }
    }
}
```

### Component Classes
Custom component classes are defined in `assets/css/main.css` using Tailwind's `@layer` directive:

```css
@layer components {
  .btn {
    @apply inline-block px-6 py-3 text-base font-medium text-center no-underline border-2 border-transparent rounded cursor-pointer transition-all duration-300 leading-none;
  }
  
  .btn-primary {
    @apply bg-primary-green text-white border-primary-green;
  }
  // ... other components
}
```

### Responsive Design
All components are built with mobile-first responsive design using Tailwind's responsive prefixes:

```html
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
  <!-- Responsive grid -->
</div>
```

## Key Features

### Consistent Styling
- All components use Tailwind utility classes
- Custom component classes for reusable elements
- Consistent color palette throughout the site
- Responsive design patterns

### Interactive Elements
- Hover effects and transitions
- Mobile-friendly navigation
- Form validation and feedback
- Modal dialogs and dropdowns

### Performance
- Optimized CSS with Tailwind's utility-first approach
- Minimal custom CSS
- Efficient responsive breakpoints
- Fast loading times

## Development

### Adding New Pages
1. Create a new PHP file in the root directory
2. Include the header and footer components
3. Use Tailwind classes for styling
4. Follow the existing color palette and design patterns

### Customizing Styles
1. Add new utility classes using Tailwind's configuration
2. Create component classes in `assets/css/main.css`
3. Use the `@layer` directive for proper CSS organization
4. Maintain consistency with the existing design system

### Database Integration
1. Use the provided database functions in `includes/database.php`
2. Follow the schema defined in `database/schema.sql`
3. Implement proper validation and sanitization
4. Use prepared statements for security

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## License

This project is proprietary software for Redolence Salon & Spa.

## Support

For technical support or questions about the website, please contact the development team.
