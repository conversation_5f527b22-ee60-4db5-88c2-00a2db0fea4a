# Troubleshooting Internal Server Error

If you're getting an Internal Server Error, follow these steps:

## Step 1: Test Basic PHP
Visit: `http://localhost/test.php`

This will test if P<PERSON> is working and if the basic files exist.

## Step 2: Debug the Loading Process
Visit: `http://localhost/debug.php`

This will show you exactly where the error occurs during the loading process.

## Step 3: Try Simple Version
Visit: `http://localhost/index_simple.php`

This is a simplified version without complex includes.

## Step 4: Check Error Logs
Look at your Apache error logs:
- Windows XAMPP: `C:\xampp\apache\logs\error.log`
- Check the most recent entries

## Common Issues and Solutions:

### 1. .htaccess Issues
The .htaccess file has been simplified for testing. If you still get errors:
- Rename `.htaccess` to `.htaccess_disabled` temporarily
- Try accessing the site again

### 2. PHP Configuration Issues
Make sure these PHP extensions are enabled in `php.ini`:
- `extension=pdo_mysql`
- `extension=mysqli`
- `extension=openssl`

### 3. File Permissions
Ensure the web server can read all files:
- All files should be readable by the web server
- Directories should be executable

### 4. Path Issues
Check if the paths in `config/app.php` are correct:
- `APP_URL` should match your local setup
- Currently set to: `http://localhost`

### 5. Missing Files
Make sure these files exist:
- `config/app.php`
- `config/database.php`
- `includes/database.php`
- `includes/auth.php`
- `includes/components/header.php`
- `includes/components/footer.php`

## If Nothing Works:

1. **Check Apache Error Log**: Look for specific error messages
2. **Enable PHP Error Display**: Add this to the top of `index.php`:
   ```php
   ini_set('display_errors', 1);
   ini_set('display_startup_errors', 1);
   error_reporting(E_ALL);
   ```

3. **Test with Minimal Code**: Start with just:
   ```php
   <?php
   echo "Hello World";
   ?>
   ```

## Contact Information
If you continue to have issues, please share:
1. The exact error message from Apache error logs
2. Your PHP version (`php -v`)
3. Your Apache version
4. Which test files work and which don't
