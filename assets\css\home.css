/**
 * Luxury Home Page Styles with Tailwind CSS
 * Redolence Salon & Spa - Premium Design
 */

@layer components {
  /* Luxury Hero Section */
  .hero-section {
    @apply relative h-screen min-h-[700px] overflow-hidden;
  }

  .hero-slider {
    @apply relative w-full h-full;
  }

  .hero-slide {
    @apply absolute top-0 left-0 w-full h-full bg-cover bg-center bg-no-repeat opacity-0 transition-all duration-1500 ease-out flex items-center;
    background-attachment: fixed;
  }

  .hero-slide.active {
    @apply opacity-100;
    animation: heroZoom 20s ease-out infinite;
  }

  .hero-overlay {
    @apply absolute top-0 left-0 w-full h-full;
    background: linear-gradient(135deg, rgba(73, 167, 92, 0.85) 0%, rgba(88, 148, 210, 0.75) 50%, rgba(73, 167, 92, 0.85) 100%);
    backdrop-filter: blur(1px);
  }

  .hero-content {
    @apply relative z-20 text-center text-white max-w-6xl mx-auto px-6;
    animation: heroContentFadeIn 1.5s ease-out;
  }

  .hero-title {
    @apply text-6xl lg:text-7xl xl:text-8xl font-bold mb-8 leading-none;
    font-family: 'Playfair Display', serif;
    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    letter-spacing: -0.02em;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .hero-subtitle {
    @apply text-xl lg:text-2xl xl:text-3xl mb-12 opacity-95 font-light leading-relaxed;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    letter-spacing: 0.05em;
  }

  .hero-buttons {
    @apply flex gap-6 justify-center flex-wrap items-center;
  }

  /* Luxury Hero Navigation */
  .hero-nav {
    @apply absolute top-1/2 -translate-y-1/2 z-30 w-full;
  }

  .hero-prev {
    @apply absolute left-8 lg:left-12;
  }

  .hero-next {
    @apply absolute right-8 lg:right-12;
  }

  .hero-prev,
  .hero-next {
    @apply w-16 h-16 lg:w-20 lg:h-20 bg-white/10 backdrop-blur-md border border-white/20 text-white rounded-full cursor-pointer transition-all duration-500 flex items-center justify-center text-xl hover:bg-white/20 hover:border-white/40 hover:scale-110 hover:-translate-y-1;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }

  /* Luxury Hero Indicators */
  .hero-indicators {
    @apply absolute bottom-12 left-1/2 -translate-x-1/2 flex gap-4 z-30;
  }

  .indicator {
    @apply w-4 h-4 rounded-full bg-white/30 border-2 border-white/50 cursor-pointer transition-all duration-500 hover:bg-white/60 hover:scale-125;
  }

  .indicator.active {
    @apply bg-white border-white scale-125;
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
  }

  /* Hero Scroll Indicator */
  .hero-scroll {
    @apply absolute bottom-8 left-1/2 -translate-x-1/2 text-white/80 text-sm font-light tracking-wider;
    animation: bounce 2s infinite;
  }

  /* Luxury Welcome Section */
  .welcome-section {
    @apply py-32 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden;
  }

  .welcome-section::before {
    content: '';
    @apply absolute top-0 left-0 w-full h-full opacity-5;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%2349a75c' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  }

  .welcome-content {
    @apply grid grid-cols-1 lg:grid-cols-2 gap-20 items-center mt-16 relative z-10;
  }

  .welcome-text {
    @apply space-y-8;
  }

  .welcome-text h3 {
    @apply text-5xl lg:text-6xl text-primary-green mb-8 font-bold leading-tight;
    background: linear-gradient(135deg, #49a75c, #5894d2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .welcome-features {
    @apply space-y-8;
  }

  .feature {
    @apply flex items-start p-6 bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-500 hover:-translate-y-2 border border-gray-100;
  }

  .feature i {
    @apply text-3xl text-primary-green mr-6 mt-2 p-3 bg-primary-green/10 rounded-xl;
  }

  .feature-content h4 {
    @apply text-xl font-semibold text-gray-900 mb-3;
  }

  .feature-content p {
    @apply text-gray-600 leading-relaxed;
  }

  .welcome-image {
    @apply relative;
  }

  .welcome-image img {
    @apply rounded-3xl shadow-2xl transform hover:scale-105 transition-transform duration-700;
  }

  .welcome-image::after {
    content: '';
    @apply absolute -top-4 -right-4 w-full h-full bg-gradient-to-br from-primary-green/20 to-primary-blue/20 rounded-3xl -z-10;
  }

  /* Luxury Featured Services Section */
  .featured-services-section {
    @apply py-32 bg-white relative;
  }

  .services-grid {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-12 mb-16;
  }

  .service-card {
    @apply bg-white rounded-3xl overflow-hidden shadow-xl transition-all duration-700 hover:-translate-y-4 hover:shadow-2xl border border-gray-100 group;
  }

  .service-image {
    @apply relative h-80 overflow-hidden;
  }

  .service-image img {
    @apply w-full h-full object-cover transition-all duration-700 group-hover:scale-110;
  }

  .service-overlay {
    @apply absolute top-0 left-0 w-full h-full bg-gradient-to-br from-primary-green/90 to-primary-blue/90 flex items-center justify-center opacity-0 transition-all duration-500 group-hover:opacity-100;
    backdrop-filter: blur(2px);
  }

  .service-overlay .btn {
    @apply transform translate-y-4 group-hover:translate-y-0 transition-transform duration-500 delay-100;
  }

  .service-content {
    @apply p-8 relative;
  }

  .service-content::before {
    content: '';
    @apply absolute top-0 left-8 w-12 h-1 bg-gradient-to-r from-primary-green to-primary-blue rounded-full;
  }

  .service-content h3 {
    @apply text-2xl font-bold text-gray-900 mb-4 mt-6 group-hover:text-primary-green transition-colors duration-300;
  }

  .service-content p {
    @apply mb-6 text-gray-600 leading-relaxed;
  }

  .service-meta {
    @apply flex justify-between items-center pt-4 border-t border-gray-100;
  }

  .duration {
    @apply flex items-center text-gray-500 text-sm font-medium;
  }

  .duration i {
    @apply mr-2 text-primary-green text-base;
  }

  .book-now {
    @apply text-primary-green font-semibold no-underline transition-all duration-300 hover:text-primary-blue flex items-center;
  }

  .book-now::after {
    content: '→';
    @apply ml-2 transition-transform duration-300;
  }

  .book-now:hover::after {
    @apply translate-x-1;
  }

  /* Luxury Categories Section */
  .categories-section {
    @apply py-32 bg-gradient-to-br from-gray-900 to-gray-800 text-white relative overflow-hidden;
  }

  .categories-section::before {
    content: '';
    @apply absolute top-0 left-0 w-full h-full opacity-10;
    background-image: radial-gradient(circle at 25% 25%, #49a75c 0%, transparent 50%), radial-gradient(circle at 75% 75%, #5894d2 0%, transparent 50%);
  }

  .categories-grid {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 relative z-10;
  }

  .category-card {
    @apply bg-white/10 backdrop-blur-lg rounded-3xl overflow-hidden shadow-2xl transition-all duration-700 text-center hover:-translate-y-6 hover:shadow-3xl border border-white/20 group;
  }

  .category-image {
    @apply h-64 overflow-hidden relative;
  }

  .category-image img {
    @apply w-full h-full object-cover transition-all duration-700 group-hover:scale-110;
  }

  .category-image::after {
    content: '';
    @apply absolute inset-0 bg-gradient-to-t from-black/50 to-transparent;
  }

  .category-content {
    @apply p-8 relative z-10;
  }

  .category-content h3 {
    @apply text-2xl font-bold text-white mb-4 group-hover:text-primary-green transition-colors duration-300;
  }

  .category-content p {
    @apply mb-8 text-gray-300 leading-relaxed;
  }

  .category-content .btn {
    @apply w-full;
  }

  /* Luxury Stats Section */
  .stats-section {
    @apply py-24 bg-gradient-to-r from-primary-green to-primary-blue text-white relative overflow-hidden;
  }

  .stats-grid {
    @apply grid grid-cols-2 lg:grid-cols-4 gap-8 text-center;
  }

  .stat-item {
    @apply space-y-4;
  }

  .stat-number {
    @apply text-5xl lg:text-6xl font-bold;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  }

  .stat-label {
    @apply text-lg font-medium opacity-90;
  }
}

}

/* Luxury Animations */
@keyframes heroZoom {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes heroContentFadeIn {
  0% { opacity: 0; transform: translateY(30px); }
  100% { opacity: 1; transform: translateY(0); }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% { transform: translate3d(0, 0, 0); }
  40%, 43% { transform: translate3d(0, -10px, 0); }
  70% { transform: translate3d(0, -5px, 0); }
  90% { transform: translate3d(0, -2px, 0); }
}

@keyframes fadeInUp {
  0% { opacity: 0; transform: translateY(30px); }
  100% { opacity: 1; transform: translateY(0); }
}

@keyframes slideInLeft {
  0% { opacity: 0; transform: translateX(-30px); }
  100% { opacity: 1; transform: translateX(0); }
}

@keyframes slideInRight {
  0% { opacity: 0; transform: translateX(30px); }
  100% { opacity: 1; transform: translateX(0); }
}

/* Scroll Animations */
.animate-on-scroll {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s ease-out;
}

.animate-on-scroll.animated {
  opacity: 1;
  transform: translateY(0);
}

/* Luxury Responsive Design */
@media (max-width: 1024px) {
  .hero-title {
    @apply text-5xl lg:text-6xl;
  }

  .hero-subtitle {
    @apply text-lg lg:text-xl;
  }

  .welcome-text h3 {
    @apply text-4xl lg:text-5xl;
  }
}

@media (max-width: 768px) {
  .hero-section {
    @apply min-h-[600px];
  }

  .hero-title {
    @apply text-4xl mb-6;
  }

  .hero-subtitle {
    @apply text-lg mb-8;
  }

  .hero-buttons {
    @apply flex-col items-center gap-4;
  }

  .hero-prev,
  .hero-next {
    @apply w-12 h-12 text-lg;
  }

  .hero-prev {
    @apply left-4;
  }

  .hero-next {
    @apply right-4;
  }

  .welcome-content {
    @apply grid-cols-1 gap-12;
  }

  .welcome-text h3 {
    @apply text-3xl mb-6;
  }

  .services-grid {
    @apply grid-cols-1 gap-8;
  }

  .categories-grid {
    @apply grid-cols-1 gap-6;
  }

  .stats-grid {
    @apply grid-cols-2 gap-6;
  }

  .stat-number {
    @apply text-4xl;
  }

  .welcome-section,
  .featured-services-section,
  .categories-section {
    @apply py-20;
  }

  .service-card,
  .category-card {
    @apply hover:-translate-y-2;
  }
}
