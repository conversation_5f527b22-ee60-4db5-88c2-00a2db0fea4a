/**
 * Home Page Styles with Tailwind CSS
 * Redolence Salon & Spa
 */

@layer components {
  /* Hero Section */
  .hero-section {
    @apply relative h-screen min-h-[600px] overflow-hidden;
  }

  .hero-slider {
    @apply relative w-full h-full;
  }

  .hero-slide {
    @apply absolute top-0 left-0 w-full h-full bg-cover bg-center bg-no-repeat opacity-0 transition-opacity duration-1000 ease-in-out flex items-center;
  }

  .hero-slide.active {
    @apply opacity-100;
  }

  .hero-overlay {
    @apply absolute top-0 left-0 w-full h-full gradient-overlay;
  }

  .hero-content {
    @apply relative z-10 text-center text-white max-w-4xl mx-auto;
  }

  .hero-title {
    @apply text-5xl font-bold mb-5 text-shadow;
  }

  .hero-subtitle {
    @apply text-xl mb-8 opacity-90;
  }

  .hero-buttons {
    @apply flex gap-5 justify-center flex-wrap;
  }

  /* Hero Navigation */
  .hero-nav {
    @apply absolute top-1/2 -translate-y-1/2 z-20;
  }

  .hero-prev {
    @apply left-8;
  }

  .hero-next {
    @apply right-8;
  }

  .hero-prev,
  .hero-next {
    @apply absolute bg-white/20 border-2 border-white/30 text-white w-12 h-12 rounded-full cursor-pointer transition-all duration-300 flex items-center justify-center;
  }

  .hero-prev:hover,
  .hero-next:hover {
    @apply bg-white/30 border-white/50;
  }

  /* Hero Indicators */
  .hero-indicators {
    @apply absolute bottom-8 left-1/2 -translate-x-1/2 flex gap-2 z-20;
  }

  .indicator {
    @apply w-3 h-3 rounded-full bg-white/50 border-none cursor-pointer transition-all duration-300;
  }

  .indicator.active {
    @apply bg-white;
  }

  /* Welcome Section */
  .welcome-section {
    @apply py-25 bg-background;
  }

  .welcome-content {
    @apply grid grid-cols-1 lg:grid-cols-2 gap-15 items-center mt-12;
  }

  .welcome-text h3 {
    @apply text-primary-green mb-5;
  }

  .welcome-features {
    @apply my-10;
  }

  .feature {
    @apply flex items-start mb-6;
  }

  .feature i {
    @apply text-2xl text-primary-green mr-5 mt-1;
  }

  .feature h4 {
    @apply mb-1 text-text-dark;
  }

  .feature p {
    @apply m-0 text-sm;
  }

  .welcome-image {
    @apply relative;
  }

  .welcome-image img {
    @apply rounded shadow-medium;
  }

  /* Featured Services Section */
  .featured-services-section {
    @apply py-25 bg-accent;
  }

  .services-grid {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10 mb-12;
  }

  .service-card {
    @apply bg-white rounded overflow-hidden shadow-light transition-all duration-300;
  }

  .service-card:hover {
    @apply -translate-y-1 shadow-medium;
  }

  .service-image {
    @apply relative h-64 overflow-hidden;
  }

  .service-image img {
    @apply w-full h-full object-cover transition-all duration-300;
  }

  .service-overlay {
    @apply absolute top-0 left-0 w-full h-full bg-primary-green/80 flex items-center justify-center opacity-0 transition-all duration-300;
  }

  .service-card:hover .service-overlay {
    @apply opacity-100;
  }

  .service-card:hover .service-image img {
    @apply scale-110;
  }

  .service-content {
    @apply p-8;
  }

  .service-content h3 {
    @apply text-text-dark mb-4;
  }

  .service-content p {
    @apply mb-5;
  }

  .service-meta {
    @apply flex justify-between items-center;
  }

  .duration {
    @apply text-text-light text-sm;
  }

  .duration i {
    @apply mr-1 text-primary-green;
  }

  .book-now {
    @apply text-primary-green font-medium no-underline transition-all duration-300;
  }

  .book-now:hover {
    @apply text-primary-blue;
  }

  /* Categories Section */
  .categories-section {
    @apply py-25 bg-background;
  }

  .categories-grid {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8;
  }

  .category-card {
    @apply bg-white rounded overflow-hidden shadow-light transition-all duration-300 text-center;
  }

  .category-card:hover {
    @apply -translate-y-1 shadow-medium;
  }

  .category-image {
    @apply h-48 overflow-hidden;
  }

  .category-image img {
    @apply w-full h-full object-cover transition-all duration-300;
  }

  .category-card:hover .category-image img {
    @apply scale-110;
  }

  .category-content {
    @apply p-8;
  }

  .category-content h3 {
    @apply text-text-dark mb-4;
  }

  .category-content p {
    @apply mb-6;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-title {
    @apply text-3xl;
  }

  .hero-subtitle {
    @apply text-lg;
  }

  .hero-buttons {
    @apply flex-col items-center;
  }

  .hero-prev,
  .hero-next {
    @apply w-10 h-10;
  }

  .hero-prev {
    @apply left-4;
  }

  .hero-next {
    @apply right-4;
  }

  .welcome-content {
    @apply grid-cols-1 gap-10;
  }

  .services-grid {
    @apply grid-cols-1;
  }

  .categories-grid {
    @apply grid-cols-1 sm:grid-cols-2;
  }

  .welcome-section,
  .featured-services-section,
  .categories-section {
    @apply py-15;
  }
}
