/**
 * Home Page Styles
 * Redolence Salon & Spa
 */

/* Hero Section */
.hero-section {
    position: relative;
    height: 100vh;
    min-height: 600px;
    overflow: hidden;
}

.hero-slider {
    position: relative;
    width: 100%;
    height: 100%;
}

.hero-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    opacity: 0;
    transition: opacity 1s ease-in-out;
    display: flex;
    align-items: center;
}

.hero-slide.active {
    opacity: 1;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(73, 167, 92, 0.8), rgba(88, 148, 210, 0.6));
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: white;
    max-width: 800px;
    margin: 0 auto;
}

.hero-title {
    font-size: var(--font-size-5xl);
    font-weight: 700;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
    font-size: var(--font-size-xl);
    margin-bottom: 30px;
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Hero Navigation */
.hero-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 3;
}

.hero-prev {
    left: 30px;
}

.hero-next {
    right: 30px;
}

.hero-prev,
.hero-next {
    position: absolute;
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-prev:hover,
.hero-next:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
}

/* Hero Indicators */
.hero-indicators {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
    z-index: 3;
}

.indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    border: none;
    cursor: pointer;
    transition: var(--transition);
}

.indicator.active {
    background: white;
}

/* Welcome Section */
.welcome-section {
    padding: 100px 0;
    background-color: var(--background-color);
}

.welcome-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
    margin-top: 50px;
}

.welcome-text h3 {
    color: var(--primary-green);
    margin-bottom: 20px;
}

.welcome-features {
    margin: 40px 0;
}

.feature {
    display: flex;
    align-items: flex-start;
    margin-bottom: 25px;
}

.feature i {
    font-size: var(--font-size-2xl);
    color: var(--primary-green);
    margin-right: 20px;
    margin-top: 5px;
}

.feature h4 {
    margin-bottom: 5px;
    color: var(--text-dark);
}

.feature p {
    margin: 0;
    font-size: var(--font-size-sm);
}

.welcome-image {
    position: relative;
}

.welcome-image img {
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-medium);
}

/* Featured Services Section */
.featured-services-section {
    padding: 100px 0;
    background-color: var(--accent-color);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 40px;
    margin-bottom: 50px;
}

.service-card {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.service-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.service-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.service-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(73, 167, 92, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
}

.service-card:hover .service-overlay {
    opacity: 1;
}

.service-card:hover .service-image img {
    transform: scale(1.1);
}

.service-content {
    padding: 30px;
}

.service-content h3 {
    color: var(--text-dark);
    margin-bottom: 15px;
}

.service-content p {
    margin-bottom: 20px;
}

.service-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.duration {
    color: var(--text-light);
    font-size: var(--font-size-sm);
}

.duration i {
    margin-right: 5px;
    color: var(--primary-green);
}

.book-now {
    color: var(--primary-green);
    font-weight: 500;
    text-decoration: none;
    transition: var(--transition);
}

.book-now:hover {
    color: var(--primary-blue);
}

/* Categories Section */
.categories-section {
    padding: 100px 0;
    background-color: var(--background-color);
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
}

.category-card {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    text-align: center;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.category-image {
    height: 200px;
    overflow: hidden;
}

.category-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.category-card:hover .category-image img {
    transform: scale(1.1);
}

.category-content {
    padding: 30px;
}

.category-content h3 {
    color: var(--text-dark);
    margin-bottom: 15px;
}

.category-content p {
    margin-bottom: 25px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: var(--font-size-3xl);
    }
    
    .hero-subtitle {
        font-size: var(--font-size-lg);
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .hero-prev,
    .hero-next {
        width: 40px;
        height: 40px;
    }
    
    .hero-prev {
        left: 15px;
    }
    
    .hero-next {
        right: 15px;
    }
    
    .welcome-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }
    
    .services-grid {
        grid-template-columns: 1fr;
    }
    
    .categories-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
    
    .welcome-section,
    .featured-services-section,
    .categories-section {
        padding: 60px 0;
    }
}
