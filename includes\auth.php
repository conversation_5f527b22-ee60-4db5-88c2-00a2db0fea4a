<?php
/**
 * Authentication Functions
 * Redolence Salon & Spa
 */

require_once __DIR__ . '/database.php';

/**
 * Start secure session
 */
function startSession() {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
}

/**
 * Check if user is logged in
 */
function isLoggedIn() {
    startSession();
    return isset($_SESSION['user_id']) && isset($_SESSION['user_type']);
}

/**
 * Check if user has specific role
 */
function hasRole($role) {
    startSession();
    return isLoggedIn() && $_SESSION['user_type'] === $role;
}

/**
 * Get current user data
 */
function getCurrentUser() {
    if (!isLoggedIn()) {
        return null;
    }
    
    $userId = $_SESSION['user_id'];
    $userType = $_SESSION['user_type'];
    
    $table = ($userType === 'admin') ? 'admins' : (($userType === 'staff') ? 'staff' : 'customers');
    
    $sql = "SELECT * FROM {$table} WHERE id = ? AND status = 'active'";
    return fetchOne($sql, [$userId]);
}

/**
 * Login user
 */
function loginUser($email, $password, $userType = 'customer') {
    $table = ($userType === 'admin') ? 'admins' : (($userType === 'staff') ? 'staff' : 'customers');
    
    // Check login attempts
    if (isAccountLocked($email)) {
        return ['success' => false, 'message' => 'Account temporarily locked due to too many failed attempts'];
    }
    
    $sql = "SELECT * FROM {$table} WHERE email = ? AND status = 'active'";
    $user = fetchOne($sql, [$email]);
    
    if (!$user || !verifyPassword($password, $user['password'])) {
        recordFailedLogin($email);
        return ['success' => false, 'message' => 'Invalid email or password'];
    }
    
    // Clear failed login attempts
    clearFailedLogins($email);
    
    // Start session
    startSession();
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['user_type'] = $userType;
    $_SESSION['user_name'] = $user['name'] ?? $user['first_name'] . ' ' . $user['last_name'];
    $_SESSION['user_email'] = $user['email'];
    $_SESSION['login_time'] = time();
    
    // Update last login
    $updateSql = "UPDATE {$table} SET last_login = NOW() WHERE id = ?";
    executeQuery($updateSql, [$user['id']]);
    
    return ['success' => true, 'user' => $user];
}

/**
 * Logout user
 */
function logoutUser() {
    startSession();
    session_destroy();
    session_start();
}

/**
 * Record failed login attempt
 */
function recordFailedLogin($email) {
    $sql = "INSERT INTO login_attempts (email, attempted_at, ip_address) VALUES (?, NOW(), ?)";
    executeQuery($sql, [$email, $_SERVER['REMOTE_ADDR']]);
}

/**
 * Clear failed login attempts
 */
function clearFailedLogins($email) {
    $sql = "DELETE FROM login_attempts WHERE email = ?";
    executeQuery($sql, [$email]);
}

/**
 * Check if account is locked
 */
function isAccountLocked($email) {
    $sql = "SELECT COUNT(*) as attempts FROM login_attempts 
            WHERE email = ? AND attempted_at > DATE_SUB(NOW(), INTERVAL ? SECOND)";
    $result = fetchOne($sql, [$email, LOGIN_LOCKOUT_TIME]);
    
    return $result['attempts'] >= MAX_LOGIN_ATTEMPTS;
}

/**
 * Require login
 */
function requireLogin($redirectUrl = '/auth/login') {
    if (!isLoggedIn()) {
        redirect($redirectUrl, 'Please login to access this page', 'warning');
    }
}

/**
 * Require specific role
 */
function requireRole($role, $redirectUrl = '/') {
    requireLogin();
    if (!hasRole($role)) {
        redirect($redirectUrl, 'Access denied', 'error');
    }
}

/**
 * Check session timeout
 */
function checkSessionTimeout() {
    startSession();
    if (isLoggedIn() && isset($_SESSION['login_time'])) {
        if (time() - $_SESSION['login_time'] > SESSION_LIFETIME) {
            logoutUser();
            redirect('/auth/login', 'Session expired. Please login again.', 'warning');
        }
    }
}

/**
 * Register customer
 */
function registerCustomer($data) {
    // Validate required fields
    $required = ['first_name', 'last_name', 'email', 'phone'];
    foreach ($required as $field) {
        if (empty($data[$field])) {
            return ['success' => false, 'message' => ucfirst(str_replace('_', ' ', $field)) . ' is required'];
        }
    }
    
    // Validate email
    if (!isValidEmail($data['email'])) {
        return ['success' => false, 'message' => 'Invalid email address'];
    }
    
    // Check if email already exists
    $existingUser = fetchOne("SELECT id FROM customers WHERE email = ?", [$data['email']]);
    if ($existingUser) {
        return ['success' => false, 'message' => 'Email already registered'];
    }
    
    // Insert customer
    $sql = "INSERT INTO customers (first_name, last_name, email, phone, created_at) 
            VALUES (?, ?, ?, ?, NOW())";
    
    try {
        $customerId = insertAndGetId($sql, [
            $data['first_name'],
            $data['last_name'],
            $data['email'],
            $data['phone']
        ]);
        
        return ['success' => true, 'customer_id' => $customerId];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Registration failed. Please try again.'];
    }
}
